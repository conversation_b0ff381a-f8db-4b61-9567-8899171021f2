#!/usr/bin/env python3
"""
测试重构后的系统
验证新架构的基本功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config.app_config import app_config
from src.core.entity_processor import EntityProcessor
from src.utils.logging_utils import get_logger, RequestContext

logger = get_logger(__name__)


async def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试重构后的系统 ===")
    
    # 创建处理器实例
    processor = EntityProcessor(app_config)
    
    try:
        # 测试配置显示
        print("\n1. 测试配置系统:")
        print(f"   缓存配置: API={app_config.cache.use_api_cache}, "
              f"主页={app_config.cache.use_homepage_cache}, "
              f"LLM={app_config.cache.use_llm_cache}, "
              f"最终结果={app_config.cache.use_final_result_cache}")
        
        # 测试日志系统
        print("\n2. 测试日志系统:")
        with RequestContext() as request_id:
            logger.info(f"测试日志消息，request_id应该是: {request_id}")
        
        # 测试数据库连接
        print("\n3. 测试数据库连接:")
        await processor._ensure_db_initialized()
        print("   数据库连接成功")
        
        # 测试缓存配置覆盖
        print("\n4. 测试缓存配置覆盖:")
        new_processor = processor.override_cache_config(
            use_api_cache=False,
            use_llm_cache=False
        )
        print(f"   新配置: API={new_processor.cache_manager.cache_config.use_api_cache}, "
              f"LLM={new_processor.cache_manager.cache_config.use_llm_cache}")
        
        print("\n✅ 基本功能测试通过")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"测试失败: {e}")
        raise
    finally:
        await processor.close()


async def test_company_info_retrieval():
    """测试公司信息获取（仅从缓存）"""
    print("\n=== 测试公司信息获取 ===")
    
    processor = EntityProcessor(app_config)
    
    try:
        # 测试获取不存在的公司信息
        test_company_id = "test_company_123"
        
        with RequestContext() as request_id:
            logger.info(f"测试获取公司信息: company_id={test_company_id}")
            
            info = await processor.get_company_info(test_company_id)
            
            if info is None:
                print(f"   ✅ 正确返回None（公司 {test_company_id} 不存在）")
            else:
                print(f"   ⚠️  意外获取到信息: {info}")
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        logger.error(f"公司信息获取测试失败: {e}")
        raise
    finally:
        await processor.close()


def test_configuration_system():
    """测试配置系统"""
    print("\n=== 测试配置系统 ===")
    
    try:
        # 测试默认配置
        print("1. 默认配置:")
        print(f"   Python版本要求: >=3.10")
        print(f"   最大工作线程: {app_config.max_workers}")
        print(f"   请求超时: {app_config.request_timeout}秒")
        print(f"   重试次数: {app_config.retry_attempts}")
        
        # 测试缓存配置
        print("\n2. 缓存配置:")
        cache_config = app_config.cache
        print(f"   API缓存: {cache_config.use_api_cache}")
        print(f"   主页缓存: {cache_config.use_homepage_cache}")
        print(f"   LLM缓存: {cache_config.use_llm_cache}")
        print(f"   最终结果缓存: {cache_config.use_final_result_cache}")
        
        # 测试日志配置
        print("\n3. 日志配置:")
        log_config = app_config.logging
        print(f"   日志级别: {log_config.level}")
        print(f"   包含request_id: {'request_id' in log_config.format}")
        
        print("\n   ✅ 配置系统测试通过")
        
    except Exception as e:
        print(f"   ❌ 配置系统测试失败: {e}")
        raise


async def main():
    """主测试函数"""
    print("开始测试重构后的实体一句话总结系统")
    print("=" * 50)
    
    try:
        # 测试配置系统
        test_configuration_system()
        
        # 测试基本功能
        await test_basic_functionality()
        
        # 测试公司信息获取
        await test_company_info_retrieval()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！重构后的系统运行正常。")
        print("\n下一步可以:")
        print("1. 使用 'uv run python -m src.main_refactored --help' 查看命令行选项")
        print("2. 使用 'uv run python -m src.main_refactored --list-config' 查看当前配置")
        print("3. 使用 'uv run python -m src.main_refactored --company-id <公司ID>' 处理公司")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
