#!/usr/bin/env python3

import sys
import asyncio
import os

# 设置PYTHONPATH
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 现在可以直接导入，让python模块自己处理相对导入
os.chdir('src')
sys.path.insert(0, '.')

async def test_entity():
    from direct_entity_processor import DirectEntityProcessor
    
    processor = DirectEntityProcessor()
    
    try:
        result = await processor.process_single_entity("华帝科技")
        print("=== 测试结果 ===")
        print(f"成功: {result.get('success', False)}")
        print(f"一句话总结: {result.get('one_liner', 'N/A')}")
        print(f"从缓存: {result.get('from_cache', False)}")
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_entity()) 