# Prompts 包

这个包包含了项目中使用的所有提示词模板，按应用目的分类存储。

## 文件结构

```
prompts/
├── __init__.py              # 包初始化文件，统一导出所有prompt
├── one_liner_prompts.py     # 一句话总结相关的提示词
├── timeline_prompts.py      # 时间线生成相关的提示词
└── README.md               # 本说明文档
```

## 使用方式

### 方式1：从包根目录导入（推荐）

```python
from prompts import ONE_LINER_USER_PROMPT, COMPANY_TIMELINE_PROMPT
```

### 方式2：从具体模块导入

```python
from prompts.one_liner_prompts import ONE_LINER_USER_PROMPT_OLD, ONE_LINER_USER_PROMPT
from prompts.timeline_prompts import COMPANY_TIMELINE_PROMPT
```

### 方式3：通过原有模块导入（向后兼容）

```python
from src.modules.prompts import COMPANY_TIMELINE_PROMPT
```

## 提示词分类

### 一句话总结 (one_liner_prompts.py)

- `ONE_LINER_USER_PROMPT_OLD`: 旧版本的一句话总结提示词，输出单个格式
- `ONE_LINER_USER_PROMPT`: 新版本的一句话总结提示词，输出三种格式
- `ONE_LINER_DISCONTINUED_FORMATS`: 已停止运营实体的格式模板（备用）

**用途**: 根据公司信息生成简洁的一句话描述，支持三种不同的格式模板。

**格式模板**:
- `___是___的___。`
- `___的___是___。`
- `___有___的___。`

### 时间线生成 (timeline_prompts.py)

- `COMPANY_TIMELINE_PROMPT`: 公司时间线生成提示词

**用途**: 根据公司的各种信息文档，生成按时间顺序组织的公司发展历程。

**特点**:
- 以天眼查数据为核心
- 按时间逆序组织内容
- 使用Markdown格式输出
- 强调数据的可信度和准确性

## 设计原则

1. **分类清晰**: 按应用目的分类，便于维护和查找
2. **向后兼容**: 保持原有导入路径的可用性
3. **统一管理**: 所有prompt集中管理，避免重复
4. **文档完善**: 每个prompt都有详细的说明和使用示例

## 添加新的提示词

1. 根据应用目的选择合适的文件，或创建新的分类文件
2. 在对应文件中添加新的prompt常量
3. 在 `__init__.py` 中添加导入和导出
4. 更新本README文档

## 注意事项

- 所有prompt都使用大写常量命名
- 支持字符串格式化的prompt使用 `{变量名}` 占位符
- 保持prompt的语言风格一致性
- 定期review和优化prompt的效果 