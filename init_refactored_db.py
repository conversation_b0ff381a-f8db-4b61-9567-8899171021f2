#!/usr/bin/env python3
"""
初始化重构后的数据库表
创建新的缓存表结构
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from src.config.mysql_config import get_mysql_url, ENGINE_CONFIG
from src.models.db_models import Base
from src.models.cache_models import Base as CacheBase
from src.utils.logging_utils import get_logger

logger = get_logger(__name__)


def init_database_tables():
    """初始化数据库表"""
    print("=== 初始化重构后的数据库表 ===")
    
    try:
        # 创建同步数据库引擎
        mysql_url = get_mysql_url(async_driver=False)
        engine = create_engine(mysql_url, **ENGINE_CONFIG)
        
        print(f"连接到数据库: {mysql_url.split('@')[1] if '@' in mysql_url else mysql_url}")
        
        # 创建原有表（如果不存在）
        print("\n1. 创建原有表结构...")
        Base.metadata.create_all(engine)
        print("   ✅ 原有表创建完成")
        
        # 创建新的缓存表
        print("\n2. 创建新的缓存表结构...")
        CacheBase.metadata.create_all(engine)
        print("   ✅ 缓存表创建完成")
        
        # 显示创建的表
        print("\n3. 已创建的表:")
        
        # 原有表
        print("   原有表:")
        for table_name in Base.metadata.tables.keys():
            print(f"     - {table_name}")
        
        # 缓存表
        print("   新缓存表:")
        for table_name in CacheBase.metadata.tables.keys():
            print(f"     - {table_name}")
        
        print(f"\n🎉 数据库表初始化完成！")
        print(f"总共创建了 {len(Base.metadata.tables) + len(CacheBase.metadata.tables)} 个表")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        logger.error(f"数据库初始化失败: {e}")
        raise


def main():
    """主函数"""
    try:
        init_database_tables()
        
        print("\n" + "=" * 50)
        print("数据库初始化完成！现在可以使用重构后的系统了。")
        print("\n建议的下一步:")
        print("1. 运行测试: uv run python test_refactored_system.py")
        print("2. 查看配置: uv run python -m src.main_refactored --list-config")
        print("3. 处理公司: uv run python -m src.main_refactored --company-id <公司ID>")
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
