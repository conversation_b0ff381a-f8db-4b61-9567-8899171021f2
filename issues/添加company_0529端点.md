# 添加 company_0529 API端点

## 任务背景
用户需要为API服务添加一个新的端点 `/company_0529/{company_id}`，调用 `get_company_info_0529` 函数。
用户后续要求修改输出格式，参考 `process_company_direct` 中 `one_liners` 的字典格式。

## 实施计划
1. **复制现有函数**：基于现有的 `get_company_info` 函数创建新版本
2. **修改路径**：使用新路径 `/company_0529/{company_id}`
3. **更新文档**：添加相应的summary和description
4. **创建新响应模型**：支持字典格式的 `one_liners` 字段
5. **修改查询逻辑**：优先从 `OneLiner0529` 表查询新格式数据

## 实施结果
✅ **第一阶段 - 基础端点**：
- 路径：`/company_0529/{company_id}`
- 函数名：`get_company_info_0529`
- 初始返回类型：`CompanyResponse`

✅ **第二阶段 - 格式优化**：
- 新增 `Company0529Response` 响应模型
- `one_liners` 字段改为 `Dict[str, Any]` 类型
- 查询逻辑：
  1. 优先从 `OneLiner0529` 表查询（包含字典格式数据）
  2. 回退到传统 `OneLiner` 表并转换格式
- 支持额外字段：`processing_method`, `limit_value`

## 技术细节
### 新Pydantic模型
```python
class Keyword(BaseModel):
    """关键词模型"""
    word: str
    start_index: int
    end_index: int
    source: str
    detail: str

class OneLinerWithKeywords(BaseModel):
    """包含关键词的一句话总结模型"""
    one_liner: str
    keywords: List[Keyword]

class Company0529Response(BaseModel):
    """公司信息响应模型（0529版本，支持关键词格式的一句话总结）"""
    id: str
    timeline: Optional[str] = None
    one_liners: Optional[OneLinerWithKeywords] = None  # 包含关键词的一句话总结
    processing_method: Optional[str] = None
    limit_value: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
```

### 查询优先级
1. **OneLiner0529表**：包含 `process_company_direct` 生成的原始JSON数据，解析为Pydantic模型
2. 如果没有找到数据，返回404错误

## 验证要点
- [x] 新端点语法正确
- [x] 使用类型安全的Pydantic模型
- [x] 完整支持关键词信息结构
- [x] 专门针对0529版本数据
- [x] 错误处理机制完整

## 使用方式
```bash
GET /company_0529/{company_id}
```

### 响应格式示例
**OneLiner0529表数据（类型安全的Pydantic模型）：**
```json
{
  "id": "78941085",
  "timeline": "公司详情...",
  "one_liners": {
    "one_liner": "通信产业是提供通信基础设施建设与数字化解决方案的国有控股企业。",
    "keywords": [
      {
        "word": "通信产业",
        "start_index": 0,
        "end_index": 3,
        "source": "公司简称",
        "detail": "重庆市通信产业服务有限公司"
      },
      {
        "word": "通信基础设施建设",
        "start_index": 6,
        "end_index": 13,
        "source": "经营信息 - 经营范围",
        "detail": "许可项目：建设工程施工..."
      }
    ]
  },
  "processing_method": "direct",
  "limit_value": 20,
  "created_at": "2024-05-29T...",
  "updated_at": "2024-05-29T..."
}
```

**如果没有找到数据：**
```json
{
  "detail": "未找到公司 78941085 的0529版本数据"
}
``` 