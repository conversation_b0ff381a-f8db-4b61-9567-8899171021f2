# 实体一句话总结

这个工具通过整合公司信息生成公司的时间线和一句话总结，并将结果保存到 MySQL 数据库中。

## 功能特点

- 从天眼查API获取公司详细信息
- 生成结构化的公司时间线
- 使用LLM生成三种格式的一句话总结
- 支持 MySQL 缓存，提高处理效率
- 将所有结果存储到 MySQL 数据库
- 支持批量处理和并发控制
- 自动跳过已处理的公司，避免重复工作
- **🆕 提供 REST API 服务，支持基于公司ID查询一句话总结**

## 主要组件

### 数据模型（`src/models/`）
- `db_models.py`：数据库模型，包含 `Company` 和 `OneLiner` 表
- `api_models.py`：API数据模型，定义请求和响应格式

### 数据获取（`src/clients/`）
- `openapi_client.py`：天眼查API客户端，支持缓存和重试机制
- `mysql_cache.py`：MySQL缓存实现，提供高性能异步缓存

### 内容生成（`src/modules/`）
- `timeline_generator.py`：使用LLM生成公司时间线
- `src/one_liner.py`：使用LLM生成一句话总结

### 处理流程（`src/company_timeline.py` & `main.py`）
- 整合API调用、内容生成和数据存储
- 提供命令行界面和批量处理功能

### **🆕 API 服务（`api_server.py`）**
- 基于 FastAPI 的 REST API 服务
- 支持根据公司ID查询一句话总结
- 提供完整的公司信息查询
- 支持公司列表和搜索功能

## 使用方法

### 基本用法

```bash
# 处理单个公司
python main.py --company-id 公司ID

# 查看已处理的公司列表
python main.py --list

# 查看特定公司的详细信息
python main.py --info 公司ID

# 清空数据库
python main.py --clear-db
```

### 批量处理

```bash
# 批量处理200家公司，使用5个并发workers
python test_batch_processing.py --count 200 --workers 5

# 指定输入输出文件
python test_batch_processing.py --input ./data/target_entity.csv --output ./data/result.csv

# 强制重新处理所有公司（不跳过已存在的）
python test_batch_processing.py --no-skip
```

### **🆕 API 服务**

#### 启动 API 服务

```bash
# 方式1：使用启动脚本
python start_api.py

# 方式2：直接运行
python api_server.py

# 方式3：使用 uvicorn
uvicorn api_server:app --host 0.0.0.0 --port 8000 --reload
```

#### API 端点

| 端点 | 方法 | 描述 | 示例 |
|------|------|------|------|
| `/` | GET | API 根路径和服务信息 | `GET /` |
| `/health` | GET | 健康检查和数据库连接状态 | `GET /health` |
| `/company/{company_id}/one-liners` | GET | 获取特定公司的一句话总结 | `GET /company/**********/one-liners` |
| `/company/{company_id}` | GET | 获取公司完整信息（含时间线） | `GET /company/**********` |
| `/companies` | GET | 获取公司列表（支持分页） | `GET /companies?page=1&size=20` |
| `/search` | GET | 根据关键词搜索公司 | `GET /search?q=234917&limit=10` |

#### API 文档

启动服务后访问：
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

#### 测试 API

```bash
# 测试所有API端点
python test_api.py

# 或使用curl测试
curl http://localhost:8000/health
curl http://localhost:8000/company/**********/one-liners
```

#### 响应示例

**获取一句话总结** (`GET /company/{company_id}/one-liners`):
```json
{
  "company_id": "**********",
  "format_1": "字节跳动是全球领先的内容科技公司。",
  "format_2": "字节跳动的产品是全球化的内容平台。",
  "format_3": "字节跳动有领先技术的创新团队。",
  "timeline": "...",
  "created_at": "2025-01-23T10:30:00Z"
}
```

**获取公司列表** (`GET /companies`):
```json
{
  "total": 150,
  "companies": [
    {
      "id": "**********",
      "created_at": "2025-01-23T10:30:00Z",
      "updated_at": "2025-01-23T10:35:00Z",
      "has_one_liner": true
    }
  ]
}
```

## 系统架构

### 缓存系统
- **三级缓存**：API缓存、时间线缓存、一句话总结缓存
- **MySQL后端**：高并发支持，连接池管理
- **自动重试**：网络异常时的容错机制

### 数据库设计
- **主表**：`companies` 存储公司基本信息和时间线
- **子表**：`one_liners` 存储三种格式的一句话总结
- **缓存表**：动态创建的MySQL缓存表

### 性能优化
- **异步处理**：所有IO操作使用async/await
- **连接池**：MySQL连接池管理
- **并发控制**：可配置的worker数量
- **智能跳过**：避免重复处理已存在的数据

## 依赖管理

安装依赖：
```bash
pip install -r requirements.txt
```

主要依赖：
- `aiohttp`：异步HTTP客户端
- `sqlalchemy`：ORM框架
- `pydantic`：数据验证
- `PyMySQL`/`aiomysql`：MySQL驱动
- `openai`：LLM客户端
- `pandas`：数据处理
- `tqdm`：进度条
- **🆕 `fastapi`：Web API 框架**
- **🆕 `uvicorn`：ASGI 服务器**

## 配置

MySQL配置在 `src/config/mysql_config.py` 中设置：
- 数据库连接信息
- 连接池配置
- 缓存表配置

## 项目结构

```
entity-one-liner/
├── src/                          # 源代码目录
│   ├── api_server.py            # API服务器
│   ├── main.py                  # 命令行工具主模块
│   ├── entity_processor.py     # 实体处理器
│   ├── company_timeline.py     # 公司时间线生成
│   ├── one_liner.py            # 一句话总结生成
│   ├── example_client.py       # API客户端示例
│   ├── test_batch_processing.py # 批量处理模块
│   ├── models/                 # 数据模型
│   │   ├── __init__.py         # 模型包初始化
│   │   ├── db_models.py        # 数据库模型
│   │   └── api_models.py       # API数据模型
│   ├── config/                 # 配置模块
│   │   ├── __init__.py         # 配置包初始化
│   │   ├── mysql_config.py     # MySQL配置
│   │   ├── cache_config.py     # 缓存配置
│   │   └── api_config.py       # API配置
│   ├── clients/                # API客户端
│   └── modules/                # 功能模块
├── run_cli.py                   # 命令行工具启动脚本
├── run_api.py                   # API服务器启动脚本
├── run_batch.py                 # 批量处理启动脚本
├── run_client.py                # API客户端示例启动脚本
├── requirements.txt             # 依赖列表
├── README.md                    # 项目说明
└── data/                        # 数据目录
```

## 测试

```bash
# 测试 MySQL 连接
python test_mysql_connection.py

# 测试 API 服务
python test_api.py
```