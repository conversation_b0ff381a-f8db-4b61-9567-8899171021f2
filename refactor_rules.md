我需要对当前项目进行一次全面的重构。请遵循以下核心原则和具体要求，一步步完成整个过程；项目的目的是根据收集来的某家公司各种信息，根据大模型，生成一句描述这家公司的话，并通过引用信息来源，增强这句话的可解释性；

**核心重构原则:**
"代码首先是给人看的，其次才是让机器执行的。" 请在整个重构过程中严格遵守这一准则。这意味着代码应当具备以下特点：
* **清晰性 (Clarity):** 变量、函数和类的命名应直观易懂，准确反映其用途。
* **模块化 (Modularity):** 将功能紧密相关的代码组织成独立的模块或类，降低耦合度。
* **简洁性 (Simplicity):** 避免不必要的复杂性，用最简单直接的方式实现功能。
* **文档化 (Documentation):** 为关键函数、类和模块添加简洁明了的文档字符串（docstrings），解释其功能、参数和返回值。
* **一致性 (Consistency):** 保持整个项目编码风格的一致性（例如，遵循PEP 8规范）。

**项目入口与核心逻辑分析:**
项目的主入口文件是 `src/main.py`。最核心的业务逻辑封装在 `src/direct_entity_processor.py` 中。请首先深入分析这两个文件，理解当前的代码是如何接收输入、调用服务、处理数据并生成最终结果的。这将是你重构工作的基础。

**具体重构任务:**

**1. 代码精简与结构优化 (Code Pruning and Restructuring):**
* 审查整个项目，识别并移除所有废弃的、未使用的或冗余的代码、文件和依赖项。
* 重构 `src/direct_entity_processor.py`，可以将其拆分为更小、更专注的类或模块。例如，可以有专门负责数据获取、数据处理、与大语言模型（LLM）交互和结果生成的模块。
* 优化 `src/main.py`，使其逻辑更清晰，主要作为项目的启动器和协调者，调用核心处理流程。

**2. 日志系统 (Logging System):**
* 实现一个健壮的日志系统（可使用Python内置的 `logging` 模块）。
* 日志应包含时间戳、日志级别（INFO, WARNING, ERROR）、模块名以及日志信息。
* **为每一个进入系统的请求生成一个唯一的 `request_id`**。这个 `request_id` 必须在整个处理链路中传递，并包含在所有与该请求相关的日志消息中，以便于追踪和调试。

**3. 引入SQLAlchemy驱动的MySQL缓存系统 (Implement SQLAlchemy-driven MySQL Caching System):**
* 所有与数据库的交互**必须使用 `SQLAlchemy`** 库来完成，以提高代码的安全性、可移植性和可维护性。你可以选择使用 SQLAlchemy Core 或 ORM。
* 创建一个统一的数据库管理模块，负责初始化引擎、会话管理（session management）和连接池。
* 定义四个与下述表结构对应的SQLAlchemy模型。
* **缓存粒度配置 (Cache Granularity Configuration):**
    * 在项目的配置文件（或通过环境变量）中，提供独立的布尔型配置项来控制每一种缓存的使用。
    * 必需的配置项如下：
        * `USE_API_CACHE` (boolean): 如果为 `true`，则在处理前尝试从 `api_results_cache` 读取数据，并在API调用成功后写入缓存。
        * `USE_HOMEPAGE_CACHE` (boolean): 如果为 `true`，则在处理前尝试从 `homepage_results_cache` 读取数据，并在主页抓取成功后写入缓存。
        * `USE_LLM_CACHE` (boolean): 如果为 `true`，则在处理前尝试从 `llm_results_cache` 读取数据，并在LLM调用成功后写入缓存。
        * `USE_FINAL_RESULT_CACHE` (boolean): 如果为 `true`，则在处理前尝试从 `final_results` 读取数据，并在最终结果生成后写入（或更新）缓存。
    * 程序在运行时，对于每一种缓存操作（读或写），都必须先检查其对应的配置开关是否为 `true`。

* **表设计:**

    * **表1: `api_results_cache` (API结果缓存)**
        * `company_id` (VARCHAR, 主键或索引)
        * `api_name` (VARCHAR)
        * `api_id` (INT)
        * `api_result` (JSON 或 TEXT) - 存储API成功返回的完整结果。
        * `update_time` (DATETIME) - 记录缓存更新的时间。

    * **表2: `homepage_results_cache` (主页结果缓存)**
        * `company_id` (VARCHAR, 主键或索引)
        * `company_url` (VARCHAR)
        * `home_page_result` (TEXT) - 存储主页抓取成功后的内容。
        * `update_time` (DATETIME)

    * **表3: `llm_results_cache` (LLM结果缓存)**
        * `company_id` (VARCHAR, 主键或索引)
        * `system_prompt` (TEXT) - 此次LLM调用的系统提示词。
        * `content` (TEXT) - 发送给LLM的用户内容。
        * `llm_result` (JSON 或 TEXT) - 存储LLM成功返回的完整结果。
        * `update_time` (DATETIME)

    * **表4: `final_results` (最终结果)**
        * `company_id` (VARCHAR, 主键)
        * `final_result` (JSON) - 存储最终生成的结构化结果。
        * `update_time` (DATETIME)

* **缓存逻辑:**
    * 对于 `api_results_cache`, `homepage_results_cache`, 和 `llm_results_cache` 这三个表，**只缓存成功获取且内容有效的数据**。如果API调用失败、主页抓取为空或LLM返回错误，则不应向这些表中写入任何记录。这些表是日志式的，保留历史成功记录。
    * 对于 `final_results` 表，`company_id` 是唯一主键。每次成功为某个 `company_id` 生成最终结果时，都应该**更新（UPDATE）**该表中的记录。如果记录不存在，则**插入（INSERT）**新记录（即 "upsert" 操作）。这确保了该表始终保存着每个公司的最新、最完整的处理结果。

**4. 最终结果格式化 (Final Output Formatting):**
最终处理流程的输出必须严格遵循以下JSON格式。请确保生成的JSON对象结构清晰，且所有字段都按要求填充。

* **JSON结构示例:**
    ```json
    {
      "one_liner": "民营企业沙钢集团（sha-steel）是一家专注于钢铁冶炼与钢材轧制的中国大型企业。",
      "keywords": [
        {
          "word": "民营企业",
          "start_index": 0,
          "end_index": 3,
          "source": "新闻信息 (5. 中国钢铁企业崛起与社会责任的双轨发展)",
          "detail": "沙钢集团，这家中国最大的民营钢铁企业，如今已拥有令人瞩目的规模。"
        },
        {
          "word": "沙钢集团",
          "start_index": 4,
          "end_index": 7,
          "source": "江苏沙钢集团有限公司 (基本信息)",
          "detail": "公司简称: 沙钢集团"
        },
        {
          "word": "sha-steel",
          "start_index": 9,
          "end_index": 17,
          "source": "江苏沙钢集团有限公司 (联系信息)",
          "detail": "官方网站: [http://www.sha-steel.com](http://www.sha-steel.com)"
        },
        {
          "word": "钢铁冶炼",
          "start_index": 23,
          "end_index": 26,
          "source": "江苏沙钢集团有限公司 (经营信息)",
          "detail": "经营范围: 钢铁冶炼，钢材轧制，金属轧制设备配件、耐火材料制品、金属结构及其构件制造，废钢收购、加工，本公司产品销售。"
        },
        {
          "word": "钢材轧制",
          "start_index": 28,
          "end_index": 31,
          "source": "江苏沙钢集团有限公司 (经营信息)",
          "detail": "经营范围: 钢铁冶炼，钢材轧制，金属轧制设备配件、耐火材料制品、金属结构及其构件制造，废钢收购、加工，本公司产品销售。"
        },
        {
          "word": "大型企业",
          "start_index": 36,
          "end_index": 39,
          "source": "新闻信息 (5. 中国钢铁企业崛起与社会责任的双轨发展)",
          "detail": "沙钢集团，这家中国最大的民营钢铁企业，如今已拥有令人瞩目的规模。其总资产高达2900亿元，员工数量超过4万名，年产钢能力更是突破4000万吨，稳居全球钢企前列。"
        }
      ]
    }
    ```

请开始重构吧。如果你对某一步有疑问，可以随时向我提问。