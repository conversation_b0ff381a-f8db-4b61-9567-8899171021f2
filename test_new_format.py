#!/usr/bin/env python3
"""
测试新的one_liner格式处理
"""
import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.direct_entity_processor import DirectEntityProcessor


async def test_new_format():
    """测试新格式处理"""
    processor = DirectEntityProcessor()
    
    try:
        # 测试公司ID
        company_id = "78941085"
        
        print(f"测试公司 {company_id}...")
        
        # 处理公司
        result = await processor.process_company_direct(
            company_id=company_id,
            save_db=False,
            use_cache=True,  # 使用缓存加快速度
            result_cache=False  # 不使用结果缓存，确保获取最新数据
        )
        
        one_liners = result.get('one_liners', {})
        print(f"\n📋 One-liner 结果:")
        print(f"类型: {type(one_liners)}")
        print(f"内容: {json.dumps(one_liners, ensure_ascii=False, indent=2)}")
        
        # 测试处理逻辑
        one_liner_text = ""
        keywords_count = 0
        keywords_list = []
        
        if isinstance(one_liners, dict):
            # 新格式：包含one_liner和keywords字段
            if 'one_liner' in one_liners:
                one_liner_text = one_liners.get('one_liner', '')
                print(f"\n✅ 一句话总结: {one_liner_text}")
                
            if 'keywords' in one_liners:
                keywords_list = one_liners.get('keywords', [])
                keywords_count = len(keywords_list)
                print(f"\n🔑 关键词数量: {keywords_count}")
                
                for i, keyword in enumerate(keywords_list[:3]):  # 显示前3个关键词
                    print(f"  {i+1}. {keyword.get('word', '')} - {keyword.get('detail', '')[:50]}...")
        
        print(f"\n📊 处理结果:")
        print(f"  - 一句话总结: {one_liner_text[:100]}...")
        print(f"  - 关键词数量: {keywords_count}")
        print(f"  - 原始数据类型: {type(one_liners)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await processor.close()


if __name__ == "__main__":
    asyncio.run(test_new_format()) 