#!/usr/bin/env python3
"""
直接实体处理器测试脚本
测试跳过timeline直接生成格式化内容的效果
"""
import sys
import os
import asyncio
import time
import argparse
import json
import logging
from typing import Dict, Any

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.direct_entity_processor import DirectEntityProcessor


async def test_direct_processing(company_id: str, use_cache: bool = True):
    """测试直接处理方法"""
    print(f"🚀 测试直接处理方法")
    print(f"公司ID: {company_id}")
    print(f"使用缓存: {'是' if use_cache else '否'}")
    print("=" * 60)
    
    processor = DirectEntityProcessor()
    
    try:
        start_time = time.time()
        
        # 直接处理
        result = await processor.process_company_direct(
            company_id, save_db=False, use_cache=use_cache
        )
        
        processing_time = time.time() - start_time
        
        print(f"✅ 直接处理完成，耗时: {processing_time:.2f}s")
        print(f"📊 结果统计:")
        print(f"   - 格式化详情长度: {len(result['formatted_details'])} 字符")
        
        print(f"\n📄 格式化的公司详情预览 (前800字符):")
        preview = result['formatted_details'][:800]
        print(f"{preview}{'...' if len(result['formatted_details']) > 800 else ''}")
        
        return result
        
    except Exception as e:
        print(f"❌ 直接处理失败: {e}")
        return None
    finally:
        await processor.close()


async def test_method_comparison(company_id: str, use_cache: bool = True):
    """测试两种方法的比较"""
    print(f"\n🔄 测试方法比较")
    print(f"公司ID: {company_id}")
    print(f"使用缓存: {'是' if use_cache else '否'}")
    print("=" * 60)
    
    processor = DirectEntityProcessor()
    
    try:
        start_time = time.time()
        
        # 比较两种方法
        comparison = await processor.compare_methods(company_id, use_cache=use_cache)
        
        comparison_time = time.time() - start_time
        
        print(f"✅ 方法比较完成，耗时: {comparison_time:.2f}s")
        
        # 输出比较结果
        print(f"\n📊 方法比较结果:")
        print(f"   直接方法:")
        print(f"     - 输入长度: {comparison['direct_method']['input_length']} 字符")
        
        print(f"   Timeline方法:")
        print(f"     - Timeline长度: {comparison['timeline_method']['timeline_length']} 字符")
        
        print(f"   差异:")
        print(f"     - 输入长度差异: {comparison['comparison']['input_length_diff']} 字符")
        
        return comparison
        
    except Exception as e:
        print(f"❌ 方法比较失败: {e}")
        return None
    finally:
        await processor.close()


async def test_multiple_companies(company_ids: list, use_cache: bool = True):
    """测试多个公司的处理效果"""
    print(f"\n🏢 测试多个公司处理")
    print(f"公司数量: {len(company_ids)}")
    print(f"使用缓存: {'是' if use_cache else '否'}")
    print("=" * 60)
    
    results = []
    
    for i, company_id in enumerate(company_ids, 1):
        print(f"\n处理第 {i}/{len(company_ids)} 家公司: {company_id}")
        print("-" * 40)
        
        result = await test_direct_processing(company_id, use_cache)
        if result:
            results.append({
                "company_id": company_id,
                "success": True,
                "details_length": len(result['formatted_details'])
            })
        else:
            results.append({
                "company_id": company_id,
                "success": False
            })
    
    # 输出总结
    print(f"\n📊 多公司处理总结:")
    success_count = sum(1 for r in results if r['success'])
    print(f"   - 成功处理: {success_count}/{len(company_ids)} 家公司")
    
    if success_count > 0:
        avg_details_length = sum(r['details_length'] for r in results if r['success']) / success_count
        
        print(f"   - 平均详情长度: {avg_details_length:.0f} 字符")
    
    return results


async def save_comparison_results(comparison: Dict[str, Any], output_file: str):
    """保存比较结果到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, ensure_ascii=False, indent=2)
        print(f"📁 比较结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="直接实体处理器测试")
    parser.add_argument("--company-id", default="2349175912", help="要测试的公司ID")
    parser.add_argument("--mode", choices=["direct", "compare", "multi"], default="direct",
                       help="测试模式: direct=直接处理, compare=方法比较, multi=多公司测试")
    parser.add_argument("--no-cache", action="store_true", help="不使用缓存")
    parser.add_argument("--companies", nargs="+", 
                       default=["2349175912", "9519792", "1234567890"],
                       help="多公司测试时的公司ID列表")
    parser.add_argument("--output", help="保存比较结果的文件路径")
    
    args = parser.parse_args()
    use_cache = not args.no_cache
    
    if args.mode == "direct":
        result = await test_direct_processing(args.company_id, use_cache)
        
    elif args.mode == "compare":
        comparison = await test_method_comparison(args.company_id, use_cache)
        
        if comparison and args.output:
            await save_comparison_results(comparison, args.output)
            
    elif args.mode == "multi":
        results = await test_multiple_companies(args.companies, use_cache)
        
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"📁 多公司测试结果已保存到: {args.output}")
            except Exception as e:
                print(f"❌ 保存结果失败: {e}")


if __name__ == "__main__":
    asyncio.run(main()) 