"""
测试模块包
包含项目的各种测试脚本
"""

# 测试模块说明
__version__ = "1.0.0"
__description__ = "Entity One-liner 项目测试模块"

# 可用的测试模块
AVAILABLE_TESTS = {
    'openapi': '完整的OpenAPI客户端测试',
    'quick': '快速OpenAPI客户端测试',
    'cache': '新缓存系统测试',
    'batch': '批量处理测试',
    'direct': '直接实体处理器测试'
}

def list_available_tests():
    """列出所有可用的测试"""
    print("可用的测试模块:")
    print("-" * 50)
    for test_name, description in AVAILABLE_TESTS.items():
        print(f"  {test_name:10} - {description}")
    print("-" * 50)
    print("\n使用方式:")
    print("  python src/test/run_tests.py <test_name>")
    print("  python src/test/run_tests.py all")
    print("  python src/test/run_tests.py --list")

if __name__ == "__main__":
    list_available_tests() 