#!/usr/bin/env python3
"""
测试运行器
提供统一的测试执行入口
"""
import sys
import os
import asyncio
import argparse
import subprocess
from typing import List, Dict

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# 可用的测试模块
AVAILABLE_TESTS = {
    'openapi': {
        'file': 'test_openapi_client.py',
        'description': '完整的OpenAPI客户端测试',
        'async': True
    },
    'quick': {
        'file': 'quick_test_openapi.py',
        'description': '快速OpenAPI客户端测试',
        'async': True
    },
    'cache': {
        'file': 'test_new_cache.py',
        'description': '新缓存系统测试',
        'async': True
    },
    'batch': {
        'file': 'test_batch_processing.py',
        'description': '批量处理测试',
        'async': True
    },
    'direct': {
        'file': 'test_direct_processor.py',
        'description': '直接实体处理器测试',
        'async': True
    }
}

def list_tests():
    """列出所有可用的测试"""
    print("可用的测试模块:")
    print("-" * 50)
    for test_name, test_info in AVAILABLE_TESTS.items():
        print(f"  {test_name:10} - {test_info['description']}")
        print(f"             文件: {test_info['file']}")
    print("-" * 50)

def run_test(test_name: str, args: List[str] = None) -> int:
    """运行指定的测试"""
    if test_name not in AVAILABLE_TESTS:
        print(f"错误: 未知的测试模块 '{test_name}'")
        list_tests()
        return 1
    
    test_info = AVAILABLE_TESTS[test_name]
    test_file = os.path.join(os.path.dirname(__file__), test_info['file'])
    
    if not os.path.exists(test_file):
        print(f"错误: 测试文件不存在 '{test_file}'")
        return 1
    
    # 构建命令
    cmd = [sys.executable, test_file]
    if args:
        cmd.extend(args)
    
    print(f"运行测试: {test_name}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # 运行测试
        result = subprocess.run(cmd, cwd=os.path.join(os.path.dirname(__file__), '..', '..'))
        return result.returncode
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 130
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return 1

def run_all_tests():
    """运行所有测试"""
    print("运行所有测试...")
    print("=" * 50)
    
    results = {}
    for test_name in AVAILABLE_TESTS.keys():
        print(f"\n开始运行: {test_name}")
        result = run_test(test_name)
        results[test_name] = result
        
        if result == 0:
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败 (退出码: {result})")
    
    # 输出总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print("-" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results.items():
        status = "✅ 通过" if result == 0 else f"❌ 失败 ({result})"
        print(f"  {test_name:10} - {status}")
        
        if result == 0:
            passed += 1
        else:
            failed += 1
    
    print("-" * 50)
    print(f"总计: {passed} 个通过, {failed} 个失败")
    
    return 0 if failed == 0 else 1

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试运行器")
    parser.add_argument("test", nargs="?", choices=list(AVAILABLE_TESTS.keys()) + ["all", "list"], 
                       help="要运行的测试模块")
    parser.add_argument("--list", action="store_true", help="列出所有可用的测试")
    
    # 解析已知参数，剩余参数传递给测试脚本
    args, unknown_args = parser.parse_known_args()
    
    if args.list or args.test == "list":
        list_tests()
        return 0
    
    if not args.test:
        print("请指定要运行的测试模块，或使用 --list 查看可用测试")
        list_tests()
        return 1
    
    if args.test == "all":
        return run_all_tests()
    else:
        return run_test(args.test, unknown_args)

if __name__ == "__main__":
    sys.exit(main()) 