import os
import sys
import csv
import random
import asyncio
import argparse
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from tqdm.asyncio import tqdm

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.entity_processor import EntityProcessor


async def process_company_batch(
    company_ids: List[str],
    max_workers: int = 5,
    input_file: str = "./data/result.csv",
    output_file: str = "./data/result_batch.csv",
    skip_existing: bool = True
) -> None:
    """
    批量处理公司，获取时间线和一句话总结
    
    Args:
        company_ids: 公司ID列表
        max_workers: 最大并行处理数
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        skip_existing: 是否跳过已存在的公司
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # 初始化处理器
    processor = EntityProcessor()
    
    # 如果启用跳过现有记录，则先过滤已存在的公司
    if skip_existing:
        print("检查已存在的公司记录...")
        filtered_company_ids = []
        for company_id in company_ids:
            try:
                existing_info = await processor.get_company_info(company_id)
                if existing_info is None:
                    filtered_company_ids.append(company_id)
                else:
                    print(f"跳过已存在的公司: {company_id}")
            except Exception as e:
                print(f"检查公司 {company_id} 时出错，将继续处理: {e}")
                filtered_company_ids.append(company_id)
        
        skipped_count = len(company_ids) - len(filtered_company_ids)
        print(f"跳过了 {skipped_count} 家已存在的公司，剩余 {len(filtered_company_ids)} 家需要处理")
        company_ids = filtered_company_ids
        
        if not company_ids:
            print("所有公司都已存在，无需处理")
            return
    
    # 创建信号量控制并发数
    semaphore = asyncio.Semaphore(max_workers)
    
    # 定义处理单个公司的任务
    async def process_single_company(company_id: str) -> Dict[str, Any]:
        async with semaphore:
            try:
                # 如果启用跳过现有记录，再次检查（双重保险）
                if skip_existing:
                    existing_info = await processor.get_company_info(company_id)
                    if existing_info is not None:
                        return {
                            "company_id": company_id,
                            "status": "skipped",
                            "timeline": existing_info.get('timeline', ''),
                            "format_1": existing_info.get('one_liners', {}).get('format_1', ''),
                            "format_2": existing_info.get('one_liners', {}).get('format_2', ''),
                            "format_3": existing_info.get('one_liners', {}).get('format_3', ''),
                            "processed_at": datetime.now().isoformat()
                        }
                
                # 获取公司详情
                company_details = await processor.company_timeline.get_company_details(company_id)
                
                # 生成时间线
                timeline = await processor.company_timeline.get_company_timeline(company_details)
                
                # 生成一句话总结
                one_liners = await processor.one_liner_generator.generate_one_liners(timeline)
                
                # 保存到数据库
                await processor._save_to_db(company_id, timeline, one_liners)
                
                return {
                    "company_id": company_id,
                    "status": "processed",
                    "timeline": timeline,
                    "format_1": one_liners[0] if len(one_liners) > 0 else "",
                    "format_2": one_liners[1] if len(one_liners) > 1 else "",
                    "format_3": one_liners[2] if len(one_liners) > 2 else "",
                    "processed_at": datetime.now().isoformat()
                }
            except Exception as e:
                print(f"处理公司 {company_id} 时出错: {e}")
                return {
                    "company_id": company_id,
                    "status": "error",
                    "error": str(e),
                    "processed_at": datetime.now().isoformat()
                }
    
    # 创建任务列表
    tasks = [process_single_company(company_id) for company_id in company_ids]
    
    # 等待所有任务完成，使用tqdm显示进度
    start_time = datetime.now()
    print(f"开始批量处理 {len(company_ids)} 家公司，最大并行数: {max_workers}")
    
    # 使用tqdm包装tasks，显示进度条
    results = await tqdm.gather(*tasks, desc="处理公司进度")
    
    end_time = datetime.now()
    
    # 计算处理时间
    processing_time = (end_time - start_time).total_seconds()
    avg_time_per_company = processing_time / len(company_ids) if company_ids else 0
    
    # 将结果写入CSV文件
    fieldnames = ["company_id", "status", "format_1", "format_2", "format_3", "timeline", "processed_at", "error"]
    
    with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow({k: v for k, v in result.items() if k in fieldnames})
    
    # 统计结果
    processed_count = sum(1 for r in results if r.get('status') == 'processed')
    skipped_count = sum(1 for r in results if r.get('status') == 'skipped')
    error_count = sum(1 for r in results if r.get('status') == 'error')
    
    print(f"处理完成，结果已写入 {output_file}")
    print(f"总处理时间: {processing_time:.2f} 秒")
    if company_ids:
        print(f"平均每家公司处理时间: {avg_time_per_company:.2f} 秒")
    print(f"新处理: {processed_count} 家")
    print(f"跳过已存在: {skipped_count} 家")
    print(f"处理失败: {error_count} 家")


def load_company_ids(input_file: str) -> List[str]:
    """
    从CSV文件加载公司ID
    
    Args:
        input_file: CSV文件路径
        
    Returns:
        公司ID列表
    """
    df = pd.read_csv(input_file)
    company_ids = df['company_id'].tolist()
    
    return company_ids


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量处理公司实体")
    parser.add_argument("--input", type=str, default="./data/result.csv", help="输入CSV文件路径")
    parser.add_argument("--output", type=str, default="./data/result_batch.csv", help="输出CSV文件路径")
    parser.add_argument("--count", type=int, default=200, help="要处理的公司数量")
    parser.add_argument("--workers", type=int, default=5, help="最大并行处理数")
    parser.add_argument("--no-skip", action="store_true", help="不跳过已存在的公司记录")
    args = parser.parse_args()
    
    # 加载公司ID
    company_ids = load_company_ids(args.input)
    
    if not company_ids:
        print(f"无法从 {args.input} 加载公司ID")
        return
    
    print(f"从 {args.input} 加载了 {len(company_ids)} 家公司")
    
    # 如果公司数量超过指定数量，随机选择
    if len(company_ids) > args.count:
        company_ids = random.sample(company_ids, args.count)
        print(f"随机选择了 {args.count} 家公司进行处理")
    
    # 处理公司
    await process_company_batch(
        company_ids=company_ids,
        max_workers=args.workers,
        input_file=args.input,
        output_file=args.output,
        skip_existing=not args.no_skip
    )


if __name__ == "__main__":
    asyncio.run(main()) 