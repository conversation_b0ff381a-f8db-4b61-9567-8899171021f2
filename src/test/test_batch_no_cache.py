#!/usr/bin/env python3
"""
批量测试脚本（不使用缓存读取）
从data/target_entity.csv中随机选取200个公司进行测试
并行度为5，不从缓存读取数据但要写到缓存
结果写到data/result_0529.csv
"""
import asyncio
import json
import logging
import sys
import os
import csv
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import pandas as pd
from tqdm.asyncio import tqdm

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.direct_entity_processor import DirectEntityProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def process_single_company(semaphore: asyncio.Semaphore, processor: DirectEntityProcessor, company: Dict[str, Any], progress_bar: tqdm) -> Dict[str, Any]:
    """处理单个公司"""
    async with semaphore:
        company_id = company['company_id']
        company_name = company['company_name']
        
        try:
            # 不从缓存读取数据，但保存到缓存
            result = await processor.process_company_direct(
                company_id=company_id,
                save_db=True,       # 写到缓存
                use_cache=False,    # 不从缓存读取
                limit=20           # 使用limit=20
            )
            
            # 提取一句话总结
            one_liners = result.get('one_liners', {})
            
            # 处理新格式的one_liner结果
            one_liner_text = ""
            keywords_count = 0
            keywords_list = []
            
            if isinstance(one_liners, dict):
                # 新格式：包含one_liner和keywords字段
                if 'one_liner' in one_liners:
                    one_liner_text = one_liners.get('one_liner', '')
                if 'keywords' in one_liners:
                    keywords_list = one_liners.get('keywords', [])
                    keywords_count = len(keywords_list)
                
                # 兼容旧格式：format_1, format_2, format_3
                if not one_liner_text:
                    one_liner_text = (one_liners.get('format_1', '') or 
                                    one_liners.get('简短版', '') or 
                                    one_liners.get('brief', ''))
            
            test_result = {
                'company_id': company_id,
                'company_name': company_name,
                'status': 'success',
                'one_liner': one_liner_text,
                'keywords_count': keywords_count,
                'keywords': keywords_list,
                'one_liners_raw': one_liners,  # 保留原始数据用于调试
                'formatted_details_length': len(result.get('formatted_details', '')),
                'processing_time': datetime.now().isoformat()
            }
            
            # 更新进度条
            progress_bar.set_postfix({
                '成功数': progress_bar.n + 1,
                '关键词数': keywords_count
            })
            progress_bar.update(1)
            
            logger.info(f"✓ 成功处理 {company_id}: {company_name}, "
                       f"关键词数: {keywords_count}")
            
        except Exception as e:
            test_result = {
                'company_id': company_id,
                'company_name': company_name,
                'status': 'error',
                'one_liner': '',
                'keywords_count': 0,
                'keywords': [],
                'one_liners_raw': {},
                'formatted_details_length': 0,
                'error_message': str(e),
                'processing_time': datetime.now().isoformat()
            }
            
            progress_bar.set_postfix({
                '状态': '失败',
                '错误': str(e)[:20] + '...' if len(str(e)) > 20 else str(e)
            })
            progress_bar.update(1)
            
            logger.error(f"✗ 处理失败 {company_id}: {company_name} - {e}")
        
        return test_result


async def test_batch_processing_no_cache():
    """测试批量处理（不使用缓存读取）"""
    max_concurrent = 5
    sample_size = 200
    
    logger.info(f"开始批量测试处理，样本数: {sample_size}, 最大并发数: {max_concurrent}")
    logger.info("配置：不从缓存读取数据，但保存到缓存")
    
    try:
        # 1. 加载目标公司数据
        data_file = os.path.join(project_root, "data", "target_entity.csv")
        if not os.path.exists(data_file):
            raise FileNotFoundError(f"找不到目标文件: {data_file}")
        
        df = pd.read_csv(data_file)
        logger.info(f"从 {data_file} 加载了 {len(df)} 条公司数据")
        
        # 随机选择200个公司
        if len(df) < sample_size:
            logger.warning(f"数据文件中只有 {len(df)} 条记录，少于要求的 {sample_size} 条")
            sample_size = len(df)
        
        test_companies = df.sample(n=sample_size, random_state=42).to_dict('records')
        
        # 确保company_id是字符串
        for company in test_companies:
            company['company_id'] = str(company['company_id'])
        
        logger.info(f"随机选择了 {len(test_companies)} 个公司进行测试")
        
        # 2. 并发处理
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        # 创建进度条
        progress_bar = tqdm(total=len(test_companies), desc="处理公司", unit="公司")
        
        # 创建共享的处理器实例
        processor = DirectEntityProcessor()
        
        try:
            # 创建并发任务
            tasks = [
                process_single_company(semaphore, processor, company, progress_bar)
                for company in test_companies
            ]
            
            # 等待所有任务完成
            logger.info(f"开始并发处理 {len(tasks)} 个公司...")
            start_time = datetime.now()
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = datetime.now()
            processing_duration = (end_time - start_time).total_seconds()
            
        finally:
            # 关闭共享的处理器实例
            await processor.close()
            # 关闭进度条
            progress_bar.close()
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                company = test_companies[i]
                error_result = {
                    'company_id': company['company_id'],
                    'company_name': company['company_name'],
                    'status': 'error',
                    'one_liner': '',
                    'keywords_count': 0,
                    'keywords': [],
                    'one_liners_raw': {},
                    'formatted_details_length': 0,
                    'error_message': str(result),
                    'processing_time': datetime.now().isoformat()
                }
                processed_results.append(error_result)
                logger.error(f"任务异常 {company['company_id']}: {result}")
            else:
                processed_results.append(result)
        
        results = processed_results
        
        # 3. 保存结果为CSV
        output_dir = os.path.join(project_root, "data")
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "result_0529.csv")
        
        # 准备CSV数据
        csv_data = []
        for result in results:
            csv_row = {
                'company_id': result['company_id'],
                'company_name': result['company_name'],
                'status': result['status'],
                'one_liner': result['one_liner'],
                'keywords_count': result['keywords_count'],
                'keywords': result['keywords'],
                'one_liners_raw': result['one_liners_raw'],
                'formatted_details_length': result['formatted_details_length'],
                'processing_time': result['processing_time'],
                'error_message': result.get('error_message', '')
            }
            csv_data.append(csv_row)
        
        # 写入CSV文件
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            if csv_data:
                fieldnames = csv_data[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(csv_data)
        
        logger.info(f"测试结果已保存到: {output_file}")
        
        # 4. 打印统计
        success_count = len([r for r in results if r['status'] == 'success'])
        failed_count = len(results) - success_count
        
        logger.info(f"批量测试完成统计:")
        logger.info(f"  总处理时间: {processing_duration:.2f} 秒")
        logger.info(f"  平均每个公司: {processing_duration/len(results):.2f} 秒")
        logger.info(f"  总数: {len(results)}")
        logger.info(f"  成功: {success_count}")
        logger.info(f"  失败: {failed_count}")
        logger.info(f"  成功率: {success_count/len(results)*100:.1f}%")
        
        if success_count > 0:
            successful_results = [r for r in results if r['status'] == 'success']
            avg_one_liners = sum(r['keywords_count'] for r in successful_results) / len(successful_results)
            avg_details_length = sum(r['formatted_details_length'] for r in successful_results) / len(successful_results)
            
            logger.info(f"  平均关键词数: {avg_one_liners:.1f}")
            logger.info(f"  平均详情长度: {avg_details_length:.0f} 字符")
        
        return results
        
    except Exception as e:
        logger.error(f"批量测试处理失败: {e}")
        raise


if __name__ == "__main__":
    logger.info("开始批量测试（不使用缓存读取）")
    logger.info("配置：随机选取200个公司，并行度5，不从缓存读取但写到缓存")
    
    try:
        asyncio.run(test_batch_processing_no_cache())
        logger.info("批量测试完成")
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1) 