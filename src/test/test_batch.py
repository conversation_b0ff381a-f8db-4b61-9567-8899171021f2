#!/usr/bin/env python3
"""
测试批量处理脚本
从target_entity.csv中随机抽取5个公司进行测试
"""
import asyncio
import json
import logging
import sys
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import pandas as pd
from tqdm.asyncio import tqdm

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.direct_entity_processor import DirectEntityProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def process_single_company(semaphore: asyncio.Semaphore, processor: DirectEntityProcessor, company: Dict[str, Any], progress_bar: tqdm) -> Dict[str, Any]:
    """处理单个公司"""

    async with semaphore:
        company_id = company['company_id']
        company_name = company['company_name']
        
        try:
            result = await processor.process_company_direct(
                company_id=company_id,
                save_db=True,
                use_cache=True,
                limit=20  # 测试时使用较小的limit
            )
            
            # 提取一句话总结
            one_liners = result.get('one_liners', {})
            
            # 处理新格式的one_liner结果
            one_liner_text = ""
            keywords_count = 0
            keywords_list = []
            
            if isinstance(one_liners, dict):
                # 新格式：包含one_liner和keywords字段
                if 'one_liner' in one_liners:
                    one_liner_text = one_liners.get('one_liner', '')
                if 'keywords' in one_liners:
                    keywords_list = one_liners.get('keywords', [])
                    keywords_count = len(keywords_list)
                
                # 兼容旧格式：format_1, format_2, format_3
                if not one_liner_text:
                    one_liner_text = (one_liners.get('format_1', '') or 
                                    one_liners.get('简短版', '') or 
                                    one_liners.get('brief', ''))
            
            test_result = {
                'company_id': company_id,
                'company_name': company_name,
                'status': 'success',
                'from_cache': result.get('from_cache', False),
                'one_liner': one_liner_text,
                'keywords_count': keywords_count,
                'keywords': keywords_list,
                'one_liners_raw': one_liners,  # 保留原始数据用于调试
                'formatted_details_length': len(result.get('formatted_details', ''))
            }
            
            # 更新进度条
            cache_status = "缓存" if test_result['from_cache'] else "新处理"
            progress_bar.set_postfix({
                '成功': len([1 for _ in range(progress_bar.n) if True]),  # 简化显示
                '来源': cache_status,
                '关键词数': keywords_count
            })
            progress_bar.update(1)
            
            logger.info(f"✓ 成功处理 {company_id}: {company_name}, "
                       f"缓存: {test_result['from_cache']}, "
                       f"关键词数: {keywords_count}")
            
        except Exception as e:
            test_result = {
                'company_id': company_id,
                'company_name': company_name,
                'status': 'error',
                'from_cache': False,
                'one_liner': '',
                'keywords_count': 0,
                'keywords': [],
                'one_liners_raw': {},
                'formatted_details_length': 0,
                'error_message': str(e)
            }
            
            progress_bar.set_postfix({
                '状态': '失败',
                '错误': str(e)[:20] + '...' if len(str(e)) > 20 else str(e)
            })
            progress_bar.update(1)
            
            logger.error(f"✗ 处理失败 {company_id}: {company_name} - {e}")
        
        return test_result


async def test_batch_processing(max_concurrent: int = 5):
    """测试批量处理"""
    logger.info(f"开始测试批量处理，最大并发数: {max_concurrent}")
    
    try:
        # 1. 加载少量测试数据
        data_file = os.path.join(project_root, "data", "target_entity.csv")
        df = pd.read_csv(data_file)
        test_companies = df.sample(n=200, random_state=42).to_dict('records')
        
        # 确保company_id是字符串
        for company in test_companies:
            company['company_id'] = str(company['company_id'])
        
        logger.info(f"选择了 {len(test_companies)} 个公司进行测试")
        
        # 2. 并发处理
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        # 创建进度条
        progress_bar = tqdm(total=len(test_companies), desc="处理公司", unit="公司")
        
        # 创建共享的处理器实例
        processor = DirectEntityProcessor()
        
        try:
            # 创建并发任务
            tasks = [
                process_single_company(semaphore, processor, company, progress_bar)
                for company in test_companies
            ]
            
            # 等待所有任务完成
            logger.info(f"开始并发处理 {len(tasks)} 个公司...")
            start_time = datetime.now()
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = datetime.now()
            processing_duration = (end_time - start_time).total_seconds()
            
        finally:
            # 关闭共享的处理器实例
            await processor.close()
            # 关闭进度条
            progress_bar.close()
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                company = test_companies[i]
                error_result = {
                    'company_id': company['company_id'],
                    'company_name': company['company_name'],
                    'status': 'error',
                    'error_message': str(result)
                }
                processed_results.append(error_result)
                logger.error(f"任务异常 {company['company_id']}: {result}")
            else:
                processed_results.append(result)
        
        results = processed_results
        
        # 3. 保存测试结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(project_root, "data")
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, f"test_batch_results_{timestamp}.json")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试结果已保存到: {output_file}")
        
        # 4. 打印统计
        success_count = len([r for r in results if r['status'] == 'success'])
        cache_count = len([r for r in results if r.get('from_cache', False)])
        
        logger.info(f"测试完成统计:")
        logger.info(f"  总数: {len(results)}")
        logger.info(f"  成功: {success_count}")
        logger.info(f"  失败: {len(results) - success_count}")
        logger.info(f"  使用缓存: {cache_count}")
        
        return results
        
    except Exception as e:
        logger.error(f"测试批量处理失败: {e}")
        raise


if __name__ == "__main__":
    # 可以通过命令行参数调整并发数，默认为5
    import sys
    max_concurrent = 5
    if len(sys.argv) > 1:
        try:
            max_concurrent = int(sys.argv[1])
        except ValueError:
            logger.warning(f"无效的并发数参数: {sys.argv[1]}，使用默认值: {max_concurrent}")
    
    asyncio.run(test_batch_processing(max_concurrent=max_concurrent)) 