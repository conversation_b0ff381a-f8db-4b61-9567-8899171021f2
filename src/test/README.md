# 测试模块

这个目录包含了项目的所有测试脚本，用于验证各个模块的功能。

## 文件结构

```
src/test/
├── __init__.py                 # 测试包初始化
├── run_tests.py               # 测试运行器（推荐使用）
├── test_openapi_client.py     # OpenAPI客户端完整测试
├── quick_test_openapi.py      # OpenAPI客户端快速测试
├── test_new_cache.py          # 新缓存系统测试
├── test_batch_processing.py   # 批量处理测试
└── README.md                  # 本说明文档
```

## 使用方式

### 方式1：使用测试运行器（推荐）

```bash
# 查看所有可用测试
python src/test/run_tests.py --list

# 运行特定测试
python src/test/run_tests.py quick
python src/test/run_tests.py openapi
python src/test/run_tests.py cache
python src/test/run_tests.py batch

# 运行所有测试
python src/test/run_tests.py all

# 向测试脚本传递参数
python src/test/run_tests.py quick --mode simple
python src/test/run_tests.py openapi --test connection
```

### 方式2：直接运行测试脚本

```bash
# 快速OpenAPI测试
python src/test/quick_test_openapi.py
python src/test/quick_test_openapi.py --mode simple

# 完整OpenAPI测试
python src/test/test_openapi_client.py
python src/test/test_openapi_client.py --test connection

# 缓存系统测试
python src/test/test_new_cache.py

# 批量处理测试
python src/test/test_batch_processing.py --count 10 --workers 2
```

## 测试模块说明

### 1. quick_test_openapi.py - 快速OpenAPI测试

**用途**: 快速验证OpenAPI客户端的基本功能

**特点**:
- 测试时间短，适合日常验证
- 包含基本的API调用和缓存测试
- 支持简单模式和完整模式

**参数**:
- `--mode simple`: 最简单的API测试
- `--mode quick`: 完整的快速测试（默认）

### 2. test_openapi_client.py - 完整OpenAPI测试

**用途**: 全面测试OpenAPI客户端的各种功能

**特点**:
- 包含7个测试模块：连接、搜索、API执行、批量处理、缓存性能、错误处理、并发请求
- 提供详细的性能统计和测试报告
- 支持单独运行特定测试

**参数**:
- `--test all`: 运行所有测试（默认）
- `--test connection`: 测试基本连接
- `--test search`: 测试实体搜索
- `--test api`: 测试API执行
- `--test batch`: 测试批量API执行
- `--test cache`: 测试缓存性能
- `--test error`: 测试错误处理
- `--test concurrent`: 测试并发请求
- `--company-id <id>`: 指定测试用的公司ID
- `--api-id <id>`: 指定测试用的API ID
- `--keyword <keyword>`: 指定测试用的搜索关键词

### 3. test_new_cache.py - 新缓存系统测试

**用途**: 验证新的缓存系统功能

**特点**:
- 测试API调用缓存（基于api_id和company_id）
- 测试搜索缓存
- 测试公司时间线缓存
- 验证缓存一致性

### 4. test_batch_processing.py - 批量处理测试

**用途**: 测试批量处理公司数据的功能

**特点**:
- 支持并发处理
- 可跳过已存在的记录
- 提供进度显示
- 输出详细的处理统计

**参数**:
- `--input <file>`: 输入CSV文件路径
- `--output <file>`: 输出CSV文件路径
- `--count <num>`: 要处理的公司数量
- `--workers <num>`: 最大并行处理数
- `--no-skip`: 不跳过已存在的公司记录

### 5. test_direct_processor.py - 直接实体处理器测试

**用途**: 测试跳过timeline直接生成one_liner的新方法

**特点**:
- 直接从公司详情生成一句话总结，跳过timeline步骤
- 支持与传统timeline方法的对比测试
- 提供多公司批量测试
- 支持结果保存到文件

**参数**:
- `--mode direct`: 直接处理测试
- `--mode compare`: 方法比较测试
- `--mode multi`: 多公司测试
- `--company-id <id>`: 指定测试的公司ID
- `--companies <id1> <id2> ...`: 多公司测试的ID列表
- `--no-cache`: 不使用缓存
- `--output <file>`: 保存结果到文件

## 测试数据

测试脚本使用以下默认测试数据：

- **公司ID**: 
  - `2349175912` (天工股份)
  - `9519792` (腾讯)
  - `1234567890` (测试ID)

- **API ID**:
  - `818` (公司基本信息)
  - `819` (其他API)

- **搜索关键词**:
  - 腾讯
  - 阿里巴巴
  - 字节跳动

## 注意事项

1. **网络依赖**: 大部分测试需要网络连接来访问OpenAPI服务
2. **数据库依赖**: 缓存测试需要MySQL数据库连接
3. **执行时间**: 完整测试可能需要几分钟时间
4. **并发限制**: 批量测试时注意控制并发数，避免对服务造成压力
5. **缓存影响**: 重复运行测试时，缓存可能影响性能测试结果

## 故障排除

### 常见问题

1. **导入错误**: 确保从项目根目录运行测试
2. **网络超时**: 检查网络连接和API服务状态
3. **数据库连接失败**: 检查MySQL配置和连接参数
4. **权限错误**: 确保有写入输出文件的权限

### 调试技巧

1. 使用快速测试先验证基本功能
2. 查看日志输出了解详细错误信息
3. 单独运行特定测试模块定位问题
4. 检查缓存状态和数据库记录

## 扩展测试

要添加新的测试：

1. 在`src/test/`目录创建新的测试文件
2. 在`run_tests.py`的`AVAILABLE_TESTS`中添加配置
3. 更新本README文档
4. 确保测试文件包含正确的导入路径 