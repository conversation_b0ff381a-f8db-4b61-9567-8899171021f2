#!/usr/bin/env python3
"""
TycOpenApiToolClient 测试脚本
测试 OpenAPI 客户端的各种功能
"""
import sys
import os
import asyncio
import logging
import time
from typing import List

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.clients.openapi_client import TycOpenApiToolClient, APICall

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class OpenAPIClientTester:
    """OpenAPI 客户端测试器"""
    
    def __init__(self):
        self.test_company_ids = [
            "2349175912",  # 天工股份
            "1234567890",  # 测试ID（可能不存在）
            "9519792",     # 腾讯
        ]
        self.test_api_ids = [
            "818",  # 公司基本信息
            "819",  # 可能的其他API
        ]
        self.test_keywords = [
            "腾讯",
            "阿里巴巴",
            "字节跳动",
        ]
    
    async def test_basic_connection(self):
        """测试基本连接"""
        print("🔗 测试基本连接...")
        
        try:
            async with TycOpenApiToolClient() as client:
                # 测试会话创建
                session = await client._ensure_session()
                print(f"✅ 会话创建成功: {type(session).__name__}")
                
                # 测试会话状态
                if not session.closed:
                    print("✅ 会话状态正常")
                else:
                    print("❌ 会话已关闭")
                    
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            return False
        
        return True
    
    async def test_search_entities(self):
        """测试实体搜索功能"""
        print("\n🔍 测试实体搜索功能...")
        
        async with TycOpenApiToolClient() as client:
            for keyword in self.test_keywords:
                print(f"\n搜索关键词: {keyword}")
                
                try:
                    # 测试使用缓存
                    start_time = time.time()
                    result_with_cache = await client._search_entities(keyword, use_cache=True)
                    cache_time = time.time() - start_time
                    
                    print(f"✅ 缓存模式 - 耗时: {cache_time:.2f}s, 结果长度: {len(str(result_with_cache))}")
                    
                    # 测试不使用缓存
                    start_time = time.time()
                    result_no_cache = await client._search_entities(keyword, use_cache=False)
                    no_cache_time = time.time() - start_time
                    
                    print(f"✅ 非缓存模式 - 耗时: {no_cache_time:.2f}s, 结果长度: {len(str(result_no_cache))}")
                    
                    # 分析结果
                    if isinstance(result_with_cache, list) and len(result_with_cache) > 0:
                        print(f"   找到 {len(result_with_cache)} 个结果")
                        first_result = result_with_cache[0]
                        if isinstance(first_result, dict):
                            print(f"   第一个结果: {first_result.get('name', 'N/A')} (ID: {first_result.get('id', 'N/A')})")
                    else:
                        print("   未找到结果或结果格式异常")
                        
                except Exception as e:
                    print(f"❌ 搜索 '{keyword}' 失败: {e}")
    
    async def test_execute_api(self):
        """测试API执行功能"""
        print("\n⚙️ 测试API执行功能...")
        
        async with TycOpenApiToolClient() as client:
            for company_id in self.test_company_ids:
                for api_id in self.test_api_ids:
                    print(f"\n测试 API {api_id} - 公司 {company_id}")
                    
                    api_call = APICall(
                        api_id=api_id,
                        name=f"test_api_{api_id}",
                        request_params={"keyword": company_id}
                    )
                    
                    try:
                        # 测试使用缓存
                        start_time = time.time()
                        result_with_cache = await client.execute_api(api_call, use_cache=True)
                        cache_time = time.time() - start_time
                        
                        print(f"✅ 缓存模式 - 耗时: {cache_time:.2f}s, 结果长度: {len(str(result_with_cache))}")
                        
                        # 测试不使用缓存
                        start_time = time.time()
                        result_no_cache = await client.execute_api(api_call, use_cache=False)
                        no_cache_time = time.time() - start_time
                        
                        print(f"✅ 非缓存模式 - 耗时: {no_cache_time:.2f}s, 结果长度: {len(str(result_no_cache))}")
                        
                        # 分析结果
                        if isinstance(result_with_cache, dict):
                            if "result" in result_with_cache:
                                print("   API调用成功，包含result字段")
                            elif "error" in result_with_cache:
                                print(f"   API返回错误: {result_with_cache.get('error', 'Unknown error')}")
                            else:
                                print("   API返回未知格式")
                        else:
                            print("   API返回非字典格式")
                            
                    except Exception as e:
                        print(f"❌ API {api_id} 调用失败: {e}")
    
    async def test_execute_apis_batch(self):
        """测试批量API执行"""
        print("\n📦 测试批量API执行...")
        
        company_id = self.test_company_ids[0]  # 使用第一个测试公司ID
        
        api_calls = [
            APICall(
                api_id=api_id,
                name=f"batch_test_{api_id}",
                request_params={"keyword": company_id}
            )
            for api_id in self.test_api_ids
        ]
        
        async with TycOpenApiToolClient() as client:
            try:
                print(f"批量执行 {len(api_calls)} 个API调用...")
                
                # 测试使用缓存
                start_time = time.time()
                results_with_cache = await client.execute_apis(api_calls, use_cache=True)
                cache_time = time.time() - start_time
                
                print(f"✅ 缓存模式 - 耗时: {cache_time:.2f}s")
                print(f"   返回结果数: {len(results_with_cache)}")
                
                for name, result in results_with_cache.items():
                    result_len = len(str(result))
                    print(f"   {name}: {result_len} 字符")
                
                # 测试不使用缓存
                start_time = time.time()
                results_no_cache = await client.execute_apis(api_calls, use_cache=False)
                no_cache_time = time.time() - start_time
                
                print(f"✅ 非缓存模式 - 耗时: {no_cache_time:.2f}s")
                print(f"   返回结果数: {len(results_no_cache)}")
                
            except Exception as e:
                print(f"❌ 批量API执行失败: {e}")
    
    async def test_cache_performance(self):
        """测试缓存性能"""
        print("\n⚡ 测试缓存性能...")
        
        company_id = self.test_company_ids[0]
        api_id = self.test_api_ids[0]
        
        api_call = APICall(
            api_id=api_id,
            name="performance_test",
            request_params={"keyword": company_id}
        )
        
        async with TycOpenApiToolClient() as client:
            # 第一次调用（写入缓存）
            print("第一次调用（写入缓存）...")
            start_time = time.time()
            await client.execute_api(api_call, use_cache=True)
            first_call_time = time.time() - start_time
            print(f"耗时: {first_call_time:.2f}s")
            
            # 多次缓存读取测试
            cache_times = []
            for i in range(5):
                start_time = time.time()
                await client.execute_api(api_call, use_cache=True)
                cache_time = time.time() - start_time
                cache_times.append(cache_time)
                print(f"缓存读取 {i+1}: {cache_time:.3f}s")
            
            avg_cache_time = sum(cache_times) / len(cache_times)
            print(f"\n缓存性能统计:")
            print(f"  首次调用: {first_call_time:.2f}s")
            print(f"  平均缓存读取: {avg_cache_time:.3f}s")
            print(f"  性能提升: {first_call_time / avg_cache_time:.1f}x")
    
    async def test_error_handling(self):
        """测试错误处理"""
        print("\n🚨 测试错误处理...")
        
        async with TycOpenApiToolClient() as client:
            # 测试无效的API ID
            invalid_api_call = APICall(
                api_id="999999",
                name="invalid_test",
                request_params={"keyword": "test"}
            )
            
            try:
                result = await client.execute_api(invalid_api_call, use_cache=False)
                print(f"✅ 无效API处理: {type(result).__name__}")
                if isinstance(result, dict) and "error" in str(result).lower():
                    print("   正确返回错误信息")
                else:
                    print("   返回了非错误结果")
            except Exception as e:
                print(f"❌ 无效API处理异常: {e}")
            
            # 测试无效的搜索关键词
            try:
                result = await client._search_entities("", use_cache=False)
                print(f"✅ 空关键词处理: {len(str(result))} 字符")
            except Exception as e:
                print(f"❌ 空关键词处理异常: {e}")
    
    async def test_concurrent_requests(self):
        """测试并发请求"""
        print("\n🔄 测试并发请求...")
        
        async with TycOpenApiToolClient() as client:
            # 创建多个并发任务
            tasks = []
            for i, company_id in enumerate(self.test_company_ids):
                api_call = APICall(
                    api_id=self.test_api_ids[0],
                    name=f"concurrent_test_{i}",
                    request_params={"keyword": company_id}
                )
                task = client.execute_api(api_call, use_cache=True)
                tasks.append(task)
            
            try:
                start_time = time.time()
                results = await asyncio.gather(*tasks, return_exceptions=True)
                total_time = time.time() - start_time
                
                success_count = sum(1 for r in results if not isinstance(r, Exception))
                error_count = len(results) - success_count
                
                print(f"✅ 并发测试完成:")
                print(f"   总耗时: {total_time:.2f}s")
                print(f"   成功请求: {success_count}")
                print(f"   失败请求: {error_count}")
                
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        print(f"   任务 {i+1} 失败: {result}")
                    else:
                        print(f"   任务 {i+1} 成功: {len(str(result))} 字符")
                        
            except Exception as e:
                print(f"❌ 并发测试失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始 TycOpenApiToolClient 全面测试\n")
        
        tests = [
            ("基本连接", self.test_basic_connection),
            ("实体搜索", self.test_search_entities),
            ("API执行", self.test_execute_api),
            ("批量API执行", self.test_execute_apis_batch),
            ("缓存性能", self.test_cache_performance),
            ("错误处理", self.test_error_handling),
            ("并发请求", self.test_concurrent_requests),
        ]
        
        results = {}
        total_start_time = time.time()
        
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"开始测试: {test_name}")
            print('='*50)
            
            try:
                start_time = time.time()
                await test_func()
                test_time = time.time() - start_time
                results[test_name] = {"status": "✅ 成功", "time": test_time}
                print(f"\n{test_name} 测试完成，耗时: {test_time:.2f}s")
            except Exception as e:
                test_time = time.time() - start_time
                results[test_name] = {"status": f"❌ 失败: {e}", "time": test_time}
                print(f"\n{test_name} 测试失败: {e}")
        
        total_time = time.time() - total_start_time
        
        # 输出测试总结
        print(f"\n{'='*50}")
        print("测试总结")
        print('='*50)
        
        for test_name, result in results.items():
            print(f"{test_name:15} | {result['status']:20} | {result['time']:6.2f}s")
        
        print(f"\n总测试时间: {total_time:.2f}s")
        
        success_count = sum(1 for r in results.values() if "成功" in r["status"])
        total_count = len(results)
        print(f"测试通过率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="TycOpenApiToolClient 测试脚本")
    parser.add_argument("--test", choices=[
        "all", "connection", "search", "api", "batch", "cache", "error", "concurrent"
    ], default="all", help="选择要运行的测试")
    parser.add_argument("--company-id", default="2349175912", help="测试用的公司ID")
    parser.add_argument("--api-id", default="818", help="测试用的API ID")
    parser.add_argument("--keyword", default="腾讯", help="测试用的搜索关键词")
    
    args = parser.parse_args()
    
    tester = OpenAPIClientTester()
    
    # 如果指定了自定义参数，更新测试数据
    if args.company_id != "2349175912":
        tester.test_company_ids = [args.company_id] + tester.test_company_ids[1:]
    if args.api_id != "818":
        tester.test_api_ids = [args.api_id] + tester.test_api_ids[1:]
    if args.keyword != "腾讯":
        tester.test_keywords = [args.keyword] + tester.test_keywords[1:]
    
    # 运行指定的测试
    if args.test == "all":
        await tester.run_all_tests()
    elif args.test == "connection":
        await tester.test_basic_connection()
    elif args.test == "search":
        await tester.test_search_entities()
    elif args.test == "api":
        await tester.test_execute_api()
    elif args.test == "batch":
        await tester.test_execute_apis_batch()
    elif args.test == "cache":
        await tester.test_cache_performance()
    elif args.test == "error":
        await tester.test_error_handling()
    elif args.test == "concurrent":
        await tester.test_concurrent_requests()


if __name__ == "__main__":
    asyncio.run(main()) 