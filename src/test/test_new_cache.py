import asyncio
import logging
import sys
import os

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.clients.openapi_client import TycOpenApiToolClient, APICall

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_new_api_cache():
    """测试新的API缓存系统"""
    print("开始测试新的API缓存系统...")
    
    async with TycOpenApiToolClient() as client:
        # 测试数据
        test_api_call = APICall(
            api_id="818", 
            name="test_api",
            request_params={"keyword": "2349175912"}
        )
        
        print(f"测试API调用: api_id={test_api_call.api_id}, company_id={test_api_call.request_params['keyword']}")
        
        # 第一次调用 - 应该从API获取数据并缓存
        print("\n第一次调用（应该从API获取数据）...")
        result1 = await client.execute_api(test_api_call, use_cache=True)
        print(f"第一次调用结果长度: {len(str(result1))}")
        
        # 第二次调用 - 应该从缓存获取数据
        print("\n第二次调用（应该从缓存获取数据）...")
        result2 = await client.execute_api(test_api_call, use_cache=True)
        print(f"第二次调用结果长度: {len(str(result2))}")
        
        # 验证结果是否一致
        if result1 == result2:
            print("✅ 缓存测试成功：两次调用结果一致")
        else:
            print("❌ 缓存测试失败：两次调用结果不一致")
        
        # 测试不使用缓存的情况
        print("\n第三次调用（不使用缓存）...")
        result3 = await client.execute_api(test_api_call, use_cache=False)
        print(f"第三次调用结果长度: {len(str(result3))}")
        
        # 测试不同company_id的缓存
        test_api_call2 = APICall(
            api_id="818", 
            name="test_api",
            request_params={"keyword": "1234567890"}
        )
        
        print(f"\n测试不同company_id: api_id={test_api_call2.api_id}, company_id={test_api_call2.request_params['keyword']}")
        result4 = await client.execute_api(test_api_call2, use_cache=True)
        print(f"不同company_id调用结果长度: {len(str(result4))}")
        
        # 测试search_entities缓存
        print("\n测试search_entities缓存...")
        search_result1 = await client._search_entities("腾讯", use_cache=True)
        print(f"第一次搜索结果长度: {len(str(search_result1))}")
        
        search_result2 = await client._search_entities("腾讯", use_cache=True)
        print(f"第二次搜索结果长度: {len(str(search_result2))}")
        
        if search_result1 == search_result2:
            print("✅ 搜索缓存测试成功：两次搜索结果一致")
        else:
            print("❌ 搜索缓存测试失败：两次搜索结果不一致")
        
        # 测试不使用搜索缓存
        print("\n测试不使用搜索缓存...")
        search_result3 = await client._search_entities("腾讯", use_cache=False)
        print(f"不使用缓存的搜索结果长度: {len(str(search_result3))}")

async def test_company_timeline_cache():
    """测试公司时间线缓存"""
    print("\n开始测试公司时间线缓存系统...")
    
    from src.company_timeline import CompanyTimeline
    
    company_timeline = CompanyTimeline()
    company_id = "2349175912"
    
    print(f"测试公司ID: {company_id}")
    
    # 第一次调用 - 应该从API获取数据并缓存
    print("\n第一次获取公司详情（应该从API获取数据）...")
    details1 = await company_timeline.get_company_details(company_id, use_cache=True)
    print(f"第一次调用结果长度: {len(str(details1))}")
    
    # 第二次调用 - 应该从缓存获取数据
    print("\n第二次获取公司详情（应该从缓存获取数据）...")
    details2 = await company_timeline.get_company_details(company_id, use_cache=True)
    print(f"第二次调用结果长度: {len(str(details2))}")
    
    if details1 == details2:
        print("✅ 公司详情缓存测试成功：两次调用结果一致")
    else:
        print("❌ 公司详情缓存测试失败：两次调用结果不一致")
    
    # 测试不使用缓存
    print("\n第三次获取公司详情（不使用缓存）...")
    details3 = await company_timeline.get_company_details(company_id, use_cache=False)
    print(f"第三次调用结果长度: {len(str(details3))}")

if __name__ == "__main__":
    async def run_all_tests():
        await test_new_api_cache()
        await test_company_timeline_cache()
    
    asyncio.run(run_all_tests()) 