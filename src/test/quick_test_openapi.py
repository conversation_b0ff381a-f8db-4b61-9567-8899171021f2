#!/usr/bin/env python3
"""
TycOpenApiToolClient 快速测试脚本
用于快速验证基本功能
"""
import sys
import os
import asyncio
import time

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.clients.openapi_client import TycOpenApiToolClient, APICall


async def quick_test():
    """快速测试主要功能"""
    print("🚀 TycOpenApiToolClient 快速测试")
    print("=" * 50)
    
    # 测试数据
    test_company_id = "23402373"  # 天工股份
    test_api_id = "1139"  # 公司基本信息API
    
    async with TycOpenApiToolClient() as client:
        print(f"✅ 客户端初始化成功")
        
        print(f"\n⚙️ 测试API调用: API {test_api_id}, 公司 {test_company_id}")
        try:
            api_call = APICall(
                api_id=test_api_id,
                name="quick_test",
                request_params={"keyword": test_company_id},
                full_response=True
            )
            
            start_time = time.time()
            api_result = await client.execute_api(api_call, use_cache=False)
            api_time = time.time() - start_time
            
            if isinstance(api_result, dict):
                result_size = len(str(api_result))
                print(f"✅ API调用成功: 返回 {result_size} 字符，耗时 {api_time:.2f}s")
                print(api_result)
                
                if "result" in api_result:
                    print("   包含result字段")
                elif "error" in api_result:
                    print(f"   API返回错误: {api_result.get('error', 'Unknown')}")
                else:
                    print("   返回格式未知")
            else:
                print(f"⚠️ API返回格式异常: {type(api_result)}")
        except Exception as e:
            print(f"❌ API调用失败: {e}")

async def simple_api_test():
    """最简单的API测试"""
    print("🔧 简单API测试")
    
    async with TycOpenApiToolClient() as client:
        api_call = APICall(
            api_id="818",
            name="simple_test",
            request_params={"keyword": "2349175912"}
        )
        
        result = await client.execute_api(api_call)
        print(f"结果类型: {type(result)}")
        print(f"结果大小: {len(str(result))} 字符")
        
        if isinstance(result, dict):
            print("主要字段:")
            for key in list(result.keys())[:5]:  # 只显示前5个字段
                print(f"  {key}: {type(result[key])}")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="TycOpenApiToolClient 快速测试")
    parser.add_argument("--mode", choices=["quick", "simple"], default="quick", 
                       help="测试模式: quick=完整快速测试, simple=最简单测试")
    
    args = parser.parse_args()
    
    if args.mode == "quick":
        await quick_test()
    else:
        await simple_api_test()


if __name__ == "__main__":
    asyncio.run(main()) 