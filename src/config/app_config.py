"""
应用程序配置模块
包含缓存粒度控制、日志配置等应用级设置
"""
import os
from typing import Optional
from dataclasses import dataclass


@dataclass
class CacheConfig:
    """缓存配置类"""
    use_api_cache: bool = True
    use_homepage_cache: bool = True
    use_llm_cache: bool = True
    use_final_result_cache: bool = True
    
    @classmethod
    def from_env(cls) -> 'CacheConfig':
        """从环境变量创建缓存配置"""
        return cls(
            use_api_cache=os.getenv('USE_API_CACHE', 'true').lower() == 'true',
            use_homepage_cache=os.getenv('USE_HOMEPAGE_CACHE', 'true').lower() == 'true',
            use_llm_cache=os.getenv('USE_LLM_CACHE', 'true').lower() == 'true',
            use_final_result_cache=os.getenv('USE_FINAL_RESULT_CACHE', 'true').lower() == 'true'
        )


@dataclass
class LoggingConfig:
    """日志配置类"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] - %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    
    @classmethod
    def from_env(cls) -> 'LoggingConfig':
        """从环境变量创建日志配置"""
        return cls(
            level=os.getenv('LOG_LEVEL', 'INFO').upper(),
            format=os.getenv('LOG_FORMAT', cls.format),
            date_format=os.getenv('LOG_DATE_FORMAT', cls.date_format)
        )


@dataclass
class AppConfig:
    """应用程序主配置类"""
    cache: CacheConfig
    logging: LoggingConfig
    
    # 应用程序设置
    max_workers: int = 5
    request_timeout: int = 30
    retry_attempts: int = 3
    
    @classmethod
    def from_env(cls) -> 'AppConfig':
        """从环境变量创建应用配置"""
        return cls(
            cache=CacheConfig.from_env(),
            logging=LoggingConfig.from_env(),
            max_workers=int(os.getenv('MAX_WORKERS', '5')),
            request_timeout=int(os.getenv('REQUEST_TIMEOUT', '30')),
            retry_attempts=int(os.getenv('RETRY_ATTEMPTS', '3'))
        )
    
    def override_cache_settings(self, **kwargs) -> 'AppConfig':
        """覆盖缓存设置并返回新的配置实例"""
        new_cache = CacheConfig(
            use_api_cache=kwargs.get('use_api_cache', self.cache.use_api_cache),
            use_homepage_cache=kwargs.get('use_homepage_cache', self.cache.use_homepage_cache),
            use_llm_cache=kwargs.get('use_llm_cache', self.cache.use_llm_cache),
            use_final_result_cache=kwargs.get('use_final_result_cache', self.cache.use_final_result_cache)
        )
        
        return AppConfig(
            cache=new_cache,
            logging=self.logging,
            max_workers=self.max_workers,
            request_timeout=self.request_timeout,
            retry_attempts=self.retry_attempts
        )


# 全局配置实例
app_config = AppConfig.from_env()
