# MySQL缓存表名配置
MYSQL_CACHE_TABLES = {
    'tyc_api_cache': 'tyc_api_cache',
    'timeline_cache': 'timeline_cache',
    'one_liner_cache': 'one_liner_cache',
    'crawl_cache': 'crawl_cache'
}

# 缓存实例的全局存储
_cache_instances = {}

def get_cache_instance(cache_type: str):
    """
    获取缓存实例（单例模式）
    
    Args:
        cache_type: 缓存类型 ('tyc_api', 'timeline', 'one_liner', 'crawl')
        
    Returns:
        缓存实例
    """
    # 检查是否已有实例
    if cache_type in _cache_instances:
        return _cache_instances[cache_type]
    
    from ..clients.mysql_cache import AsyncMySQLCache, AsyncMySQLApiCache, AsyncMySQLCrawlCache
    
    table_mapping = {
        'tyc_api': MYSQL_CACHE_TABLES['tyc_api_cache'],
        'timeline': MYSQL_CACHE_TABLES['timeline_cache'],
        'one_liner': MYSQL_CACHE_TABLES['one_liner_cache'],
        'crawl': MYSQL_CACHE_TABLES['crawl_cache']
    }
    
    table_name = table_mapping.get(cache_type)
    if not table_name:
        raise ValueError(f"未知的缓存类型: {cache_type}")
    
    # 根据缓存类型创建对应实例
    if cache_type == 'tyc_api':
        instance = AsyncMySQLApiCache(table_name)
    elif cache_type == 'crawl':
        instance = AsyncMySQLCrawlCache(table_name)
    else:
        # 其他类型继续使用原来的AsyncMySQLCache
        instance = AsyncMySQLCache(table_name)
    
    # 缓存实例
    _cache_instances[cache_type] = instance
    return instance

def clear_cache_instances():
    """清理所有缓存实例（用于测试和资源清理）"""
    global _cache_instances
    _cache_instances = {} 