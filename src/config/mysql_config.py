import os
from typing import Optional

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': '3d891bb7e9ff49bd9fe6b1ef5745e125in01.internal.cn-north-4.mysql.rds.myhuaweicloud.com',
    'port': 3306,
    'user': 'jdtest_d_zhuan_ddl',
    'password': 'NhnB!v3YCI4OnytUWO',
    'database': 'one_liner',
    'charset': 'utf8mb4'
}

# 数据库连接URL构建函数
def get_mysql_url(async_driver: bool = False) -> str:
    """
    构建MySQL连接URL
    
    Args:
        async_driver: 是否使用异步驱动
        
    Returns:
        MySQL连接URL
    """
    driver = "mysql+aiomysql" if async_driver else "mysql+pymysql"
    
    return (f"{driver}://{MYSQL_CONFIG['user']}:{MYSQL_CONFIG['password']}"
            f"@{MYSQL_CONFIG['host']}:{MYSQL_CONFIG['port']}"
            f"/{MYSQL_CONFIG['database']}?charset={MYSQL_CONFIG['charset']}")

# 缓存表配置
CACHE_TABLES = {
    'tyc_api_cache': 'tyc_api_cache',
    'timeline_cache': 'timeline_cache', 
    'one_liner_cache': 'one_liner_cache'
}

# 数据库引擎配置
ENGINE_CONFIG = {
    'pool_size': 10,
    'max_overflow': 20,
    'pool_timeout': 30,
    'pool_recycle': 3600,
    'echo': False
} 