"""
缓存管理模块
统一管理四种类型的缓存操作
"""
from typing import Any, Optional, Dict
from sqlalchemy import select
from sqlalchemy.dialects.mysql import insert as mysql_insert

from ..models.cache_models import ApiResultsCache, HomepageResultsCache, LlmResultsCache, FinalResults
from ..config.app_config import CacheConfig
from ..utils.logging_utils import get_logger
from .database_manager import db_manager

logger = get_logger(__name__)


class CacheManager:
    """缓存管理器，统一管理所有缓存操作"""
    
    def __init__(self, cache_config: CacheConfig):
        self.cache_config = cache_config
    
    async def get_api_cache(self, company_id: str, api_name: str, api_id: int) -> Optional[str]:
        """获取API缓存结果"""
        if not self.cache_config.use_api_cache:
            logger.debug(f"API缓存已禁用，跳过获取 company_id={company_id}, api_name={api_name}")
            return None
        
        async def _get_operation(session):
            result = await session.execute(
                select(ApiResultsCache.api_result)
                .where(
                    ApiResultsCache.company_id == company_id,
                    ApiResultsCache.api_name == api_name,
                    ApiResultsCache.api_id == api_id
                )
            )
            row = result.first()
            return row[0] if row else None
        
        try:
            result = await db_manager.execute_in_session(_get_operation)
            if result:
                logger.debug(f"从API缓存获取数据成功: company_id={company_id}, api_name={api_name}")
            return result
        except Exception as e:
            logger.warning(f"获取API缓存失败: {e}")
            return None
    
    async def set_api_cache(self, company_id: str, api_name: str, api_id: int, api_result: str) -> None:
        """设置API缓存结果"""
        if not self.cache_config.use_api_cache:
            logger.debug(f"API缓存已禁用，跳过设置 company_id={company_id}, api_name={api_name}")
            return
        
        async def _set_operation(session):
            # 使用MySQL的ON DUPLICATE KEY UPDATE语法
            stmt = mysql_insert(ApiResultsCache).values(
                company_id=company_id,
                api_name=api_name,
                api_id=api_id,
                api_result=api_result
            )
            stmt = stmt.on_duplicate_key_update(
                api_result=stmt.inserted.api_result,
                update_time=stmt.inserted.update_time
            )
            await session.execute(stmt)
            await session.commit()
        
        try:
            await db_manager.execute_in_session(_set_operation)
            logger.debug(f"设置API缓存成功: company_id={company_id}, api_name={api_name}")
        except Exception as e:
            logger.warning(f"设置API缓存失败: {e}")
    
    async def get_homepage_cache(self, company_id: str, company_url: str) -> Optional[str]:
        """获取主页缓存结果"""
        if not self.cache_config.use_homepage_cache:
            logger.debug(f"主页缓存已禁用，跳过获取 company_id={company_id}")
            return None
        
        async def _get_operation(session):
            result = await session.execute(
                select(HomepageResultsCache.home_page_result)
                .where(
                    HomepageResultsCache.company_id == company_id,
                    HomepageResultsCache.company_url == company_url
                )
            )
            row = result.first()
            return row[0] if row else None
        
        try:
            result = await db_manager.execute_in_session(_get_operation)
            if result:
                logger.debug(f"从主页缓存获取数据成功: company_id={company_id}")
            return result
        except Exception as e:
            logger.warning(f"获取主页缓存失败: {e}")
            return None
    
    async def set_homepage_cache(self, company_id: str, company_url: str, home_page_result: str) -> None:
        """设置主页缓存结果"""
        if not self.cache_config.use_homepage_cache:
            logger.debug(f"主页缓存已禁用，跳过设置 company_id={company_id}")
            return
        
        async def _set_operation(session):
            stmt = mysql_insert(HomepageResultsCache).values(
                company_id=company_id,
                company_url=company_url,
                home_page_result=home_page_result
            )
            stmt = stmt.on_duplicate_key_update(
                home_page_result=stmt.inserted.home_page_result,
                update_time=stmt.inserted.update_time
            )
            await session.execute(stmt)
            await session.commit()
        
        try:
            await db_manager.execute_in_session(_set_operation)
            logger.debug(f"设置主页缓存成功: company_id={company_id}")
        except Exception as e:
            logger.warning(f"设置主页缓存失败: {e}")
    
    async def get_llm_cache(self, company_id: str, system_prompt: str, content: str) -> Optional[str]:
        """获取LLM缓存结果"""
        if not self.cache_config.use_llm_cache:
            logger.debug(f"LLM缓存已禁用，跳过获取 company_id={company_id}")
            return None
        
        async def _get_operation(session):
            result = await session.execute(
                select(LlmResultsCache.llm_result)
                .where(
                    LlmResultsCache.company_id == company_id,
                    LlmResultsCache.system_prompt == system_prompt,
                    LlmResultsCache.content == content
                )
            )
            row = result.first()
            return row[0] if row else None
        
        try:
            result = await db_manager.execute_in_session(_get_operation)
            if result:
                logger.debug(f"从LLM缓存获取数据成功: company_id={company_id}")
            return result
        except Exception as e:
            logger.warning(f"获取LLM缓存失败: {e}")
            return None
    
    async def set_llm_cache(self, company_id: str, system_prompt: str, content: str, llm_result: str) -> None:
        """设置LLM缓存结果"""
        if not self.cache_config.use_llm_cache:
            logger.debug(f"LLM缓存已禁用，跳过设置 company_id={company_id}")
            return
        
        async def _set_operation(session):
            stmt = mysql_insert(LlmResultsCache).values(
                company_id=company_id,
                system_prompt=system_prompt,
                content=content,
                llm_result=llm_result
            )
            stmt = stmt.on_duplicate_key_update(
                llm_result=stmt.inserted.llm_result,
                update_time=stmt.inserted.update_time
            )
            await session.execute(stmt)
            await session.commit()
        
        try:
            await db_manager.execute_in_session(_set_operation)
            logger.debug(f"设置LLM缓存成功: company_id={company_id}")
        except Exception as e:
            logger.warning(f"设置LLM缓存失败: {e}")
    
    async def get_final_result(self, company_id: str) -> Optional[Dict[str, Any]]:
        """获取最终结果"""
        if not self.cache_config.use_final_result_cache:
            logger.debug(f"最终结果缓存已禁用，跳过获取 company_id={company_id}")
            return None
        
        async def _get_operation(session):
            result = await session.execute(
                select(FinalResults.final_result)
                .where(FinalResults.company_id == company_id)
            )
            row = result.first()
            return row[0] if row else None
        
        try:
            result = await db_manager.execute_in_session(_get_operation)
            if result:
                logger.debug(f"从最终结果缓存获取数据成功: company_id={company_id}")
            return result
        except Exception as e:
            logger.warning(f"获取最终结果缓存失败: {e}")
            return None
    
    async def set_final_result(self, company_id: str, final_result: Dict[str, Any]) -> None:
        """设置最终结果（upsert操作）"""
        if not self.cache_config.use_final_result_cache:
            logger.debug(f"最终结果缓存已禁用，跳过设置 company_id={company_id}")
            return
        
        async def _set_operation(session):
            stmt = mysql_insert(FinalResults).values(
                company_id=company_id,
                final_result=final_result
            )
            stmt = stmt.on_duplicate_key_update(
                final_result=stmt.inserted.final_result,
                update_time=stmt.inserted.update_time
            )
            await session.execute(stmt)
            await session.commit()
        
        try:
            await db_manager.execute_in_session(_set_operation)
            logger.debug(f"设置最终结果缓存成功: company_id={company_id}")
        except Exception as e:
            logger.warning(f"设置最终结果缓存失败: {e}")
