"""
LLM交互模块
负责与大语言模型的交互和结果处理
"""
import json
from typing import Dict, Any, Optional

from ..one_liner import OneLiner
from ..utils.logging_utils import get_logger
from .cache_manager import CacheManager

logger = get_logger(__name__)


class LLMInteraction:
    """LLM交互器，负责与大语言模型的交互"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.one_liner_generator = OneLiner()
    
    async def generate_one_liner_summary(self, company_id: str, formatted_content: str) -> Dict[str, Any]:
        """
        生成一句话总结
        
        Args:
            company_id: 公司ID
            formatted_content: 格式化的公司内容
            
        Returns:
            一句话总结字典
        """
        logger.info(f"开始生成一句话总结: company_id={company_id}")
        
        try:
            # 获取系统提示词（这里需要从OneLiner类中获取）
            # 由于OneLiner类可能没有直接暴露系统提示词，我们使用一个标准的标识符
            system_prompt = "one_liner_generation_prompt"  # 这是一个标识符
            
            # 检查LLM缓存
            cached_result = await self.cache_manager.get_llm_cache(
                company_id, system_prompt, formatted_content
            )
            
            if cached_result:
                logger.info(f"从LLM缓存获取一句话总结: company_id={company_id}")
                try:
                    return json.loads(cached_result)
                except json.JSONDecodeError as e:
                    logger.warning(f"LLM缓存结果JSON解析失败: {e}")
                    # 继续执行新的LLM调用
            
            # 调用LLM生成一句话总结
            one_liners_json = await self.one_liner_generator.generate_one_liners(
                formatted_content, 
                use_cache=self.cache_manager.cache_config.use_llm_cache
            )
            
            # 解析JSON结果
            try:
                one_liners = json.loads(one_liners_json)
                if not one_liners:
                    one_liners = {}
            except json.JSONDecodeError as e:
                logger.error(f"解析一句话总结JSON失败: {e}")
                one_liners = {}
            
            # 保存到LLM缓存
            if one_liners:
                await self.cache_manager.set_llm_cache(
                    company_id, system_prompt, formatted_content, one_liners_json
                )
            
            logger.info(f"成功生成一句话总结: company_id={company_id}")
            return one_liners
            
        except Exception as e:
            logger.error(f"生成一句话总结失败: company_id={company_id}, error={e}")
            raise
    
    async def close(self):
        """关闭资源"""
        try:
            if hasattr(self.one_liner_generator, 'close'):
                await self.one_liner_generator.close()
        except Exception as e:
            logger.warning(f"关闭LLMInteraction资源时出错: {e}")
