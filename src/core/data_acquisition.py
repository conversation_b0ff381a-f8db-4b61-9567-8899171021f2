"""
数据获取模块
负责从各种数据源获取公司信息
"""
from typing import Dict, Any, Optional
import json

from ..company_timeline import CompanyTimeline
from ..clients.crawl_markdown_client import crawl_company_info
from ..utils.logging_utils import get_logger
from .cache_manager import CacheManager

logger = get_logger(__name__)


class DataAcquisition:
    """数据获取器，负责从API和网页获取公司数据"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.company_timeline = CompanyTimeline()
    
    async def get_company_api_data(self, company_id: str) -> Dict[str, Any]:
        """
        获取公司API数据
        
        Args:
            company_id: 公司ID
            
        Returns:
            公司API数据字典
        """
        logger.info(f"开始获取公司API数据: company_id={company_id}")
        
        # 使用现有的CompanyTimeline获取数据，它内部已经处理了缓存
        company_details = await self.company_timeline.get_company_details(
            company_id, 
            use_cache=self.cache_manager.cache_config.use_api_cache
        )
        
        if not company_details:
            raise ValueError(f"无法获取公司 {company_id} 的API数据")
        
        # 检查公司类型，跳过机关
        orgtype = await self.company_timeline.get_company_orgtype(company_details)
        if orgtype == '机关':
            raise ValueError(f"公司 {company_id} 为机关类型，跳过处理")
        
        logger.info(f"成功获取公司API数据: company_id={company_id}")
        return company_details
    
    async def get_company_homepage_data(self, company_id: str, company_details: Dict[str, Any]) -> Optional[str]:
        """
        获取公司主页数据
        
        Args:
            company_id: 公司ID
            company_details: 公司API数据
            
        Returns:
            主页内容的Markdown文本，如果获取失败则返回None
        """
        logger.info(f"开始获取公司主页数据: company_id={company_id}")
        
        try:
            # 获取公司主页URL
            homepage_url = await self.company_timeline.get_company_homepage_url(company_details)
            
            if not homepage_url:
                logger.warning(f"公司 {company_id} 没有主页URL")
                return None
            
            logger.debug(f"公司主页URL: {homepage_url}")
            
            # 检查缓存
            cached_result = await self.cache_manager.get_homepage_cache(company_id, homepage_url)
            if cached_result:
                logger.info(f"从缓存获取公司主页数据: company_id={company_id}")
                return cached_result
            
            # 爬取主页内容
            homepage_markdown = await crawl_company_info(
                homepage_url, 
                use_cache=self.cache_manager.cache_config.use_homepage_cache
            )
            
            if homepage_markdown and homepage_markdown.strip():
                # 保存到缓存
                await self.cache_manager.set_homepage_cache(company_id, homepage_url, homepage_markdown)
                logger.info(f"成功获取并缓存公司主页数据: company_id={company_id}")
                return homepage_markdown
            else:
                logger.warning(f"公司 {company_id} 主页内容为空")
                return None
                
        except Exception as e:
            logger.error(f"获取公司主页数据失败: company_id={company_id}, error={e}")
            return None
    
    async def close(self):
        """关闭资源"""
        try:
            if hasattr(self.company_timeline, 'close'):
                await self.company_timeline.close()
        except Exception as e:
            logger.warning(f"关闭DataAcquisition资源时出错: {e}")
