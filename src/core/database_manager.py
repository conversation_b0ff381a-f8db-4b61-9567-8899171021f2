"""
数据库管理模块
统一管理数据库连接、会话和操作
"""
import asyncio
from typing import Optional, AsyncGenerator
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, AsyncEngine
from sqlalchemy.orm import sessionmaker

from ..config.mysql_config import get_mysql_url, ENGINE_CONFIG
from ..utils.logging_utils import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """数据库管理器，负责数据库连接和会话管理"""
    
    def __init__(self):
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._lock = asyncio.Lock()
    
    async def initialize(self) -> None:
        """初始化数据库连接"""
        async with self._lock:
            if self._engine is None:
                mysql_async_url = get_mysql_url(async_driver=True)
                self._engine = create_async_engine(mysql_async_url, **ENGINE_CONFIG)
                
                self._session_factory = sessionmaker(
                    bind=self._engine,
                    class_=AsyncSession,
                    expire_on_commit=False
                )
                
                logger.info("数据库连接已初始化")
    
    async def close(self) -> None:
        """关闭数据库连接"""
        async with self._lock:
            if self._engine:
                await self._engine.dispose()
                self._engine = None
                self._session_factory = None
                logger.info("数据库连接已关闭")
    
    @property
    def engine(self) -> AsyncEngine:
        """获取数据库引擎"""
        if self._engine is None:
            raise RuntimeError("数据库未初始化，请先调用 initialize() 方法")
        return self._engine
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话的上下文管理器"""
        if self._session_factory is None:
            await self.initialize()
        
        session = self._session_factory()
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    async def execute_in_session(self, operation, *args, **kwargs):
        """在会话中执行操作"""
        async with self.get_session() as session:
            return await operation(session, *args, **kwargs)


# 全局数据库管理器实例
db_manager = DatabaseManager()
