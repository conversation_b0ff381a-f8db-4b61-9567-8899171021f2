"""
数据处理模块
负责格式化和处理从各种数据源获取的原始数据
"""
import json
from typing import Dict, Any, Optional

from ..utils.open_api_compression import (
    process_patent_api_response,
    process_product_api_response,
    process_news_api_response,
    process_controller_api_response,
    process_investment_api_response,
    process_development_api_response,
    process_company_api_response
)
from ..utils.logging_utils import get_logger

logger = get_logger(__name__)


class DataProcessor:
    """数据处理器，负责格式化和处理原始数据"""
    
    def __init__(self):
        # API名称到处理函数的映射
        self.api_processors = {
            'Patent': process_patent_api_response,
            'Products': process_product_api_response,
            'News': process_news_api_response,
            'Actual Controller': process_controller_api_response,
            'Investment': process_investment_api_response,
            'Development': process_development_api_response,
            'Basic': process_company_api_response,
        }
    
    def format_company_details(self, company_details: Dict[str, Any], limit: int = 20) -> str:
        """
        将公司详情格式化为Markdown文本
        
        Args:
            company_details: 公司详情字典
            limit: 每个API数据的最大条目数，默认20
            
        Returns:
            格式化后的Markdown文本
        """
        logger.debug(f"开始格式化公司详情，limit={limit}")
        
        formatted_sections = []
        
        for api_name, api_data in company_details.items():
            try:
                # 提取实际的API结果
                api_result = api_data.get('result', api_data)
                
                # 如果api_result包含Code=300000，表示无结果，则跳过
                if isinstance(api_result, str) and api_result.find('Code=300000') != -1:
                    logger.debug(f"跳过API {api_name}，返回码为300000（无结果）")
                    continue
                
                if not api_result:
                    logger.debug(f"跳过API {api_name}，结果为空")
                    continue
                
                # 解析JSON数据
                if isinstance(api_result, str):
                    try:
                        api_data_parsed = json.loads(api_result)
                    except json.JSONDecodeError as e:
                        logger.warning(f"API {api_name} JSON解析失败: {e}")
                        continue
                else:
                    api_data_parsed = api_result
                
                # 查找匹配的处理函数
                processor_func = self.api_processors.get(api_name)
                
                if processor_func:
                    # 使用对应的处理函数转换为Markdown
                    markdown_content = processor_func(api_data_parsed, limit=limit)
                    
                    if markdown_content and markdown_content.strip():
                        formatted_sections.append(markdown_content)
                        logger.debug(f"成功处理API {api_name}，生成内容长度: {len(markdown_content)}")
                    else:
                        logger.debug(f"API {api_name} 处理后内容为空")
                else:
                    logger.warning(f"未找到API {api_name} 的处理函数")
                    # 使用简单格式作为备用
                    content = str(api_data_parsed)
                    if content and content.strip():
                        section = f"# {api_name.replace('_', ' ').title()}\n\n{content}\n"
                        formatted_sections.append(section)
                
            except Exception as e:
                logger.error(f"处理API {api_name} 时出错: {e}")
                # 出错时使用简单格式
                try:
                    content = str(api_data)
                    if content and content.strip():
                        section = f"# {api_name.replace('_', ' ').title()}\n\n{content}\n"
                        formatted_sections.append(section)
                except Exception as e2:
                    logger.error(f"API {api_name} 的备用格式化也失败: {e2}")
                    continue
        
        # 合并所有部分
        final_content = "\n\n---\n\n".join(formatted_sections)
        
        logger.info(f"公司详情格式化完成，生成内容长度: {len(final_content)}")
        return final_content.strip()
    
    def combine_data_sources(self, formatted_api_data: str, homepage_data: Optional[str] = None) -> str:
        """
        合并不同数据源的内容
        
        Args:
            formatted_api_data: 格式化的API数据
            homepage_data: 主页数据（可选）
            
        Returns:
            合并后的内容
        """
        logger.debug("开始合并数据源")
        
        combined_content = formatted_api_data
        
        if homepage_data and homepage_data.strip():
            combined_content = f"{formatted_api_data}\n\n---\n\n{homepage_data}"
            logger.debug(f"已合并主页数据，总长度: {len(combined_content)}")
        else:
            logger.debug("没有主页数据需要合并")
        
        return combined_content
    
    def validate_processed_data(self, processed_data: str) -> bool:
        """
        验证处理后的数据是否有效
        
        Args:
            processed_data: 处理后的数据
            
        Returns:
            数据是否有效
        """
        if not processed_data or not processed_data.strip():
            logger.warning("处理后的数据为空")
            return False
        
        # 检查最小长度要求
        min_length = 100  # 最少100个字符
        if len(processed_data) < min_length:
            logger.warning(f"处理后的数据长度不足: {len(processed_data)} < {min_length}")
            return False
        
        logger.debug(f"数据验证通过，长度: {len(processed_data)}")
        return True
