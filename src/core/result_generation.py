"""
结果生成模块
负责生成最终的结构化结果，包括关键词标注和来源引用
"""
import re
import json
from typing import Dict, Any, List, Optional, Tuple

from ..utils.logging_utils import get_logger

logger = get_logger(__name__)


class ResultGenerator:
    """结果生成器，负责生成最终的结构化结果"""
    
    def __init__(self):
        pass
    
    def generate_final_result(
        self, 
        company_id: str, 
        one_liners: Dict[str, Any], 
        company_details: Dict[str, Any],
        formatted_content: str
    ) -> Dict[str, Any]:
        """
        生成最终的结构化结果
        
        Args:
            company_id: 公司ID
            one_liners: 一句话总结字典
            company_details: 原始公司详情
            formatted_content: 格式化的内容
            
        Returns:
            最终的结构化结果
        """
        logger.info(f"开始生成最终结果: company_id={company_id}")
        
        try:
            # 选择最佳的一句话总结
            selected_one_liner = self._select_best_one_liner(one_liners)
            
            if not selected_one_liner:
                logger.warning(f"没有找到有效的一句话总结: company_id={company_id}")
                return {
                    "one_liner": "",
                    "keywords": []
                }
            
            # 提取关键词并标注来源
            keywords = self._extract_keywords_with_sources(
                selected_one_liner, 
                company_details, 
                formatted_content
            )
            
            final_result = {
                "one_liner": selected_one_liner,
                "keywords": keywords
            }
            
            logger.info(f"成功生成最终结果: company_id={company_id}, keywords_count={len(keywords)}")
            return final_result
            
        except Exception as e:
            logger.error(f"生成最终结果失败: company_id={company_id}, error={e}")
            raise
    
    def _select_best_one_liner(self, one_liners: Dict[str, Any]) -> Optional[str]:
        """
        从多个一句话总结中选择最佳的一个
        
        Args:
            one_liners: 一句话总结字典
            
        Returns:
            选择的一句话总结
        """
        if not one_liners:
            return None
        
        # 优先级顺序：详细版 > 正式版 > 简短版 > 其他格式
        priority_keys = [
            'detailed', '详细版', 'format_2',
            'formal', '正式版', 'format_3', 
            'brief', '简短版', 'format_1'
        ]
        
        for key in priority_keys:
            if key in one_liners and one_liners[key]:
                logger.debug(f"选择一句话总结格式: {key}")
                return one_liners[key]
        
        # 如果没有找到优先级中的键，返回第一个非空值
        for key, value in one_liners.items():
            if value and isinstance(value, str):
                logger.debug(f"使用备选一句话总结格式: {key}")
                return value
        
        return None
    
    def _extract_keywords_with_sources(
        self, 
        one_liner: str, 
        company_details: Dict[str, Any],
        formatted_content: str
    ) -> List[Dict[str, Any]]:
        """
        从一句话总结中提取关键词并标注来源
        
        Args:
            one_liner: 一句话总结
            company_details: 原始公司详情
            formatted_content: 格式化的内容
            
        Returns:
            关键词列表，每个关键词包含位置和来源信息
        """
        logger.debug("开始提取关键词和来源")
        
        keywords = []
        
        # 定义需要标注的关键词模式
        keyword_patterns = [
            # 公司类型
            r'(民营企业|国有企业|外资企业|合资企业|股份公司|有限公司)',
            # 公司名称相关
            r'([A-Za-z0-9\-\.]+(?:集团|公司|企业|科技|技术))',
            # 行业相关
            r'(钢铁冶炼|钢材轧制|制造业|服务业|科技|金融|房地产|医疗|教育)',
            # 规模相关
            r'(大型企业|中型企业|小型企业|龙头企业|领先企业)',
            # 地域相关
            r'(中国|北京|上海|深圳|广州|江苏|浙江|山东|河南|湖北|湖南|四川|重庆)',
        ]
        
        # 合并所有模式
        combined_pattern = '|'.join(f'({pattern})' for pattern in keyword_patterns)
        
        # 在一句话总结中查找关键词
        for match in re.finditer(combined_pattern, one_liner):
            keyword = match.group()
            start_index = match.start()
            end_index = match.end() - 1  # 调整为包含结束位置
            
            # 查找来源
            source_info = self._find_keyword_source(keyword, company_details, formatted_content)
            
            keyword_info = {
                "word": keyword,
                "start_index": start_index,
                "end_index": end_index,
                "source": source_info["source"],
                "detail": source_info["detail"]
            }
            
            keywords.append(keyword_info)
        
        logger.debug(f"提取到 {len(keywords)} 个关键词")
        return keywords
    
    def _find_keyword_source(
        self, 
        keyword: str, 
        company_details: Dict[str, Any],
        formatted_content: str
    ) -> Dict[str, str]:
        """
        查找关键词的来源信息
        
        Args:
            keyword: 关键词
            company_details: 原始公司详情
            formatted_content: 格式化的内容
            
        Returns:
            包含来源和详情的字典
        """
        # 在格式化内容中搜索关键词
        lines = formatted_content.split('\n')
        
        for i, line in enumerate(lines):
            if keyword in line:
                # 向上查找最近的标题（以#开头的行）
                section_title = "未知来源"
                for j in range(i, -1, -1):
                    if lines[j].strip().startswith('#'):
                        section_title = lines[j].strip().lstrip('#').strip()
                        break
                
                # 提取包含关键词的句子或段落作为详情
                detail = line.strip()
                if not detail:
                    # 如果当前行为空，查找前后的非空行
                    for k in range(max(0, i-2), min(len(lines), i+3)):
                        if lines[k].strip() and keyword in lines[k]:
                            detail = lines[k].strip()
                            break
                
                return {
                    "source": section_title,
                    "detail": detail[:200] + "..." if len(detail) > 200 else detail
                }
        
        # 如果在格式化内容中没找到，尝试在原始数据中查找
        for api_name, api_data in company_details.items():
            api_result = api_data.get('result', api_data)
            if isinstance(api_result, str) and keyword in api_result:
                return {
                    "source": f"{api_name} (API数据)",
                    "detail": f"在{api_name}数据中找到相关信息"
                }
        
        # 默认返回
        return {
            "source": "系统推断",
            "detail": f"基于公司信息推断得出的{keyword}"
        }
