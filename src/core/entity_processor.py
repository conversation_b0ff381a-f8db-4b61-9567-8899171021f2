"""
实体处理器核心模块
协调所有子模块完成公司信息处理流程
"""
from typing import Dict, Any, Optional

from ..config.app_config import AppConfig
from ..utils.logging_utils import get_logger, RequestContext
from .database_manager import db_manager
from .cache_manager import CacheManager
from .data_acquisition import DataAcquisition
from .data_processing import DataProcessor
from .llm_interaction import LLMInteraction
from .result_generation import ResultGenerator

logger = get_logger(__name__)


class EntityProcessor:
    """
    实体处理器
    协调数据获取、处理、LLM交互和结果生成的完整流程
    """
    
    def __init__(self, app_config: AppConfig):
        self.app_config = app_config
        self.cache_manager = CacheManager(app_config.cache)
        self.data_acquisition = DataAcquisition(self.cache_manager)
        self.data_processor = DataProcessor()
        self.llm_interaction = LLMInteraction(self.cache_manager)
        self.result_generator = ResultGenerator()
        
        # 初始化数据库
        self._db_initialized = False
    
    async def _ensure_db_initialized(self):
        """确保数据库已初始化"""
        if not self._db_initialized:
            await db_manager.initialize()
            self._db_initialized = True
    
    async def process_company(
        self, 
        company_id: str, 
        limit: int = 20,
        save_to_cache: bool = True
    ) -> Dict[str, Any]:
        """
        处理公司信息的完整流程
        
        Args:
            company_id: 公司ID
            limit: 每个API数据的最大条目数
            save_to_cache: 是否保存到缓存
            
        Returns:
            处理结果字典
        """
        # 使用请求上下文管理request_id
        with RequestContext() as request_id:
            logger.info(f"开始处理公司: company_id={company_id}, limit={limit}")
            
            try:
                await self._ensure_db_initialized()
                
                # 1. 检查最终结果缓存
                final_result = await self.cache_manager.get_final_result(company_id)
                if final_result:
                    logger.info(f"从最终结果缓存获取数据: company_id={company_id}")
                    return {
                        "company_id": company_id,
                        "final_result": final_result,
                        "from_cache": True,
                        "request_id": request_id
                    }
                
                # 2. 数据获取阶段
                logger.info(f"开始数据获取阶段: company_id={company_id}")
                company_api_data = await self.data_acquisition.get_company_api_data(company_id)
                homepage_data = await self.data_acquisition.get_company_homepage_data(
                    company_id, company_api_data
                )
                
                # 3. 数据处理阶段
                logger.info(f"开始数据处理阶段: company_id={company_id}")
                formatted_api_data = self.data_processor.format_company_details(
                    company_api_data, limit=limit
                )
                
                # 验证处理后的数据
                if not self.data_processor.validate_processed_data(formatted_api_data):
                    raise ValueError(f"公司 {company_id} 的数据处理后无效")
                
                # 合并数据源
                combined_content = self.data_processor.combine_data_sources(
                    formatted_api_data, homepage_data
                )
                
                # 4. LLM交互阶段
                logger.info(f"开始LLM交互阶段: company_id={company_id}")
                one_liners = await self.llm_interaction.generate_one_liner_summary(
                    company_id, combined_content
                )
                
                # 5. 结果生成阶段
                logger.info(f"开始结果生成阶段: company_id={company_id}")
                final_result = self.result_generator.generate_final_result(
                    company_id, one_liners, company_api_data, combined_content
                )
                
                # 6. 保存最终结果到缓存
                if save_to_cache:
                    await self.cache_manager.set_final_result(company_id, final_result)
                    logger.info(f"已保存最终结果到缓存: company_id={company_id}")
                
                result = {
                    "company_id": company_id,
                    "final_result": final_result,
                    "from_cache": False,
                    "request_id": request_id,
                    "processing_details": {
                        "api_data_length": len(str(company_api_data)),
                        "formatted_content_length": len(combined_content),
                        "homepage_data_available": homepage_data is not None,
                        "one_liners_count": len(one_liners) if one_liners else 0,
                        "keywords_count": len(final_result.get("keywords", []))
                    }
                }
                
                logger.info(f"公司处理完成: company_id={company_id}")
                return result
                
            except Exception as e:
                logger.error(f"处理公司失败: company_id={company_id}, error={e}")
                raise
    
    async def get_company_info(self, company_id: str) -> Optional[Dict[str, Any]]:
        """
        获取公司信息（仅从缓存）
        
        Args:
            company_id: 公司ID
            
        Returns:
            公司信息字典，如果不存在则返回None
        """
        with RequestContext() as request_id:
            logger.info(f"获取公司信息: company_id={company_id}")
            
            try:
                await self._ensure_db_initialized()
                
                final_result = await self.cache_manager.get_final_result(company_id)
                if final_result:
                    return {
                        "company_id": company_id,
                        "final_result": final_result,
                        "request_id": request_id
                    }
                
                logger.info(f"公司信息不存在: company_id={company_id}")
                return None
                
            except Exception as e:
                logger.error(f"获取公司信息失败: company_id={company_id}, error={e}")
                return None
    
    def override_cache_config(self, **cache_settings) -> 'EntityProcessor':
        """
        创建具有不同缓存配置的新处理器实例
        
        Args:
            **cache_settings: 缓存设置覆盖
            
        Returns:
            新的EntityProcessor实例
        """
        new_config = self.app_config.override_cache_settings(**cache_settings)
        return EntityProcessor(new_config)
    
    async def close(self):
        """关闭所有资源"""
        logger.info("开始关闭EntityProcessor资源")
        
        try:
            await self.data_acquisition.close()
            await self.llm_interaction.close()
            await db_manager.close()
            logger.info("EntityProcessor资源关闭完成")
        except Exception as e:
            logger.warning(f"关闭EntityProcessor资源时出错: {e}")
