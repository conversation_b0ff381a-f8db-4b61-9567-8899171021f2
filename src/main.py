import os
import sys
import asyncio
import argparse

from .direct_entity_processor import DirectEntityProcessor


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="实体一句话总结处理工具")
    parser.add_argument("--company-id", type=str, help="要处理的公司ID")
    parser.add_argument("--list", action="store_true", help="列出已处理的公司")
    parser.add_argument("--info", type=str, help="查看特定公司的详细信息")
    parser.add_argument("--save-db", action="store_true", help="保存中间文件")
    parser.add_argument("--no-cache", action="store_true", help="不使用缓存（全局开关）")
    
    # 细粒度缓存控制参数
    cache_group = parser.add_argument_group('细粒度缓存控制', '控制不同层级的缓存使用')
    cache_group.add_argument("--no-result-cache", action="store_true", 
                           help="不使用结果级缓存（跳过从one_liner缓存表获取完整结果）")
    cache_group.add_argument("--no-api-cache", action="store_true", 
                           help="不使用API级缓存（强制重新调用OpenAPI）")
    cache_group.add_argument("--no-crawl-cache", action="store_true", 
                           help="不使用爬取级缓存（强制重新爬取网页）")
    cache_group.add_argument("--no-llm-cache", action="store_true", 
                           help="不使用LLM级缓存（强制重新生成一句话总结）")
    cache_group.add_argument("--no-save-cache", action="store_true", 
                           help="不保存结果到缓存表（但仍可保存到数据库）")
    
    # 其他操作参数
    parser.add_argument("--clear-db", action="store_true", help="清空数据库中的结果表")
    parser.add_argument("--clear-cache", action="store_true", help="清空one_liner缓存表")
    parser.add_argument("--list-cache", action="store_true", help="列出缓存条目")
    args = parser.parse_args()
    
    processor = DirectEntityProcessor()
    
    try:
        if args.clear_db:
            # 清空数据库中的结果表
            await processor.clear_database()
        elif args.clear_cache:
            # 清空one_liner缓存表
            await processor.clear_cache()
            print("one_liner缓存表已清空")
        elif args.list_cache:
            # 列出缓存条目
            cache_entries = await processor.list_cache_entries()
            if not cache_entries:
                print("OneLiner0529缓存表中暂无数据")
            else:
                print(f"OneLiner0529缓存条目 (共{len(cache_entries)}条):")
                for entry in cache_entries:
                    print(f"公司ID: {entry['company_id']}, "
                          f"处理方法: {entry['processing_method']}, "
                          f"Limit: {entry['limit_value']}, "
                          f"创建时间: {entry['created_at']}, "
                          f"更新时间: {entry['updated_at']}, "
                          f"详情长度: {entry['details_length']} 字符, "
                          f"总结数量: {entry['one_liner_count']}, "
                          f"有总结: {'是' if entry['has_one_liner'] else '否'}")
        elif args.info:
            # 查看特定公司的信息
            company_info = await processor.get_company_info(args.info)
            if company_info:
                print(f"公司ID: {company_info['id']}")
                print(f"创建时间: {company_info['created_at']}")
                print(f"更新时间: {company_info['updated_at']}")
                print("\n时间线片段 (前100字符):")
                print(f"{company_info['timeline'][:100]}...")
            else:
                print(f"未找到公司信息: {args.info}")
        elif args.list:
            # 列出已处理的公司
            companies = await processor.list_companies()
            if not companies:
                print("数据库中尚无公司信息")
            else:
                print(f"已处理的公司 (共{len(companies)}家):")
                for company in companies:
                    print(f"ID: {company['id']}, 更新时间: {company['updated_at']}, 一句话总结数: {company['one_liner_count']}")
        elif args.company_id:
            # 处理指定公司，应用细粒度缓存控制
            
            # 计算缓存参数
            use_cache = not args.no_cache  # 全局缓存开关
            
            # 细粒度缓存控制（None表示使用全局设置）
            result_cache = None if not args.no_result_cache else False
            api_cache = None if not args.no_api_cache else False  
            crawl_cache = None if not args.no_crawl_cache else False
            llm_cache = None if not args.no_llm_cache else False
            save_cache = None if not args.no_save_cache else False
            
            # 输出当前缓存配置
            print("=== 缓存配置 ===")
            print(f"全局缓存: {'启用' if use_cache else '禁用'}")
            if args.no_result_cache or args.no_api_cache or args.no_crawl_cache or args.no_llm_cache or args.no_save_cache:
                print("细粒度配置:")
                if args.no_result_cache:
                    print("  - 结果缓存: 禁用")
                if args.no_api_cache:
                    print("  - API缓存: 禁用")
                if args.no_crawl_cache:
                    print("  - 爬取缓存: 禁用")
                if args.no_llm_cache:
                    print("  - LLM缓存: 禁用")
                if args.no_save_cache:
                    print("  - 保存缓存: 禁用")
            print("=" * 20)
            
            await processor.process_company_direct(
                args.company_id, 
                save_db=args.save_db, 
                use_cache=use_cache,
                result_cache=result_cache,
                api_cache=api_cache,
                crawl_cache=crawl_cache,
                llm_cache=llm_cache,
                save_cache=save_cache
            )
        else:
            # 显示帮助信息，包括使用示例
            parser.print_help()
            print("\n=== 使用示例 ===")
            print("1. 完全不使用缓存:")
            print("   python -m src.main --company-id 2353364615 --no-cache")
            
            print("\n2. 只禁用LLM缓存，强制重新生成总结:")
            print("   python -m src.main --company-id 2353364615 --no-llm-cache")
            
            print("\n3. 跳过结果缓存，但使用其他缓存:")
            print("   python -m src.main --company-id 2353364615 --no-result-cache")
            
            print("\n4. 只使用API缓存，其他都重新生成:")
            print("   python -m src.main --company-id 2353364615 --no-cache --no-api-cache=False")
            print("   （注意：由于命令行限制，建议直接调用API）")
            
            print("\n5. 禁用爬取和LLM缓存:")
            print("   python -m src.main --company-id 2353364615 --no-crawl-cache --no-llm-cache")
            
    finally:
        # 确保在程序结束前关闭数据库连接
        await processor.close()


if __name__ == "__main__":
    asyncio.run(main()) 