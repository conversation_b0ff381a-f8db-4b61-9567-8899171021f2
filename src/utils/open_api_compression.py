import json
from typing import Dict, List, Any
import datetime


def extract_patent_data(api_response: str, limit: int = 20) -> List[Dict[str, str]]:
    """
    从专利API响应中提取指定字段
    
    Args:
        api_response: 专利API返回的JSON字符串
        limit: 提取数据的最大数量，默认20
        
    Returns:
        包含提取字段的专利数据列表
    """
    try:
        # 解析JSON字符串
        data = json.loads(api_response) if isinstance(api_response, str) else api_response
        
        # 检查响应结构
        if not data.get('result') or not data['result'].get('items'):
            return []
        
        # 提取指定字段，限制数量
        extracted_patents = []
        items = data['result']['items'][:limit]  # 限制提取数量
        for item in items:
            patent_info = {
                'title': item.get('title', ''),
                'cat': item.get('cat', ''),
                'patentType': item.get('patentType', ''),
                'patentStatus': item.get('patentStatus', ''),
                'abstracts': item.get('abstracts', ''),
                'applicantname': item.get('applicantname', ''),
                'pubDate': item.get('pubDate', ''),
                'applicationPublishTime': item.get('applicationPublishTime', '')
            }
            extracted_patents.append(patent_info)
        
        return extracted_patents
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"数据提取错误: {e}")
        return []


def extract_product_data(api_response, limit: int = 20) -> List[Dict[str, str]]:
    """
    从产品API响应中提取指定字段
    
    Args:
        api_response: 产品API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        包含提取字段的产品数据列表
    """
    try:
        # 解析JSON字符串
        data = json.loads(api_response) if isinstance(api_response, str) else api_response
        
        # 检查响应结构
        if not data.get('result') or not data['result'].get('items'):
            return []
        
        # 提取指定字段，限制数量
        extracted_products = []
        items = data['result']['items'][:limit]  # 限制提取数量
        for item in items:
            product_info = {
                'name': item.get('name', ''),
                'filterName': item.get('filterName', ''),
                'type': item.get('type', ''),
                'brief': item.get('brief', ''),
                'classes': item.get('classes', '')
            }
            extracted_products.append(product_info)
        
        return extracted_products
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"数据提取错误: {e}")
        return []


def extract_news_data(api_response, limit: int = 20) -> List[Dict[str, str]]:
    """
    从新闻API响应中提取指定字段
    
    Args:
        api_response: 新闻API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        包含提取字段的新闻数据列表
    """
    try:
        # 解析JSON字符串
        data = json.loads(api_response) if isinstance(api_response, str) else api_response
        
        # 检查响应结构
        if not data.get('result') or not data['result'].get('items'):
            return []
        
        # 提取指定字段，限制数量
        extracted_news = []
        items = data['result']['items'][:limit]  # 限制提取数量
        for item in items:
            # 处理tags字段：将列表合并为分号连接的字符串
            tags = item.get('tags', [])
            tags_str = '；'.join(tags) if isinstance(tags, list) else str(tags)
            
            # 处理rtm时间戳：除以1000后转换为日期格式
            rtm = item.get('rtm', '')
            date_str = ''
            if rtm:
                try:
                    # 除以1000转换为秒，然后转换为日期
                    timestamp = int(rtm) / 1000
                    date_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                except:
                    date_str = str(rtm)
            
            news_info = {
                'title': item.get('title', ''),
                'abstracts': item.get('abstracts', ''),
                'website': item.get('website', ''),
                'tags': tags_str,
                'rtm': date_str
            }
            extracted_news.append(news_info)
        
        return extracted_news
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"数据提取错误: {e}")
        return []


def extract_controller_data(api_response, limit: int = 20) -> List[Dict[str, str]]:
    """
    从实际控制人API响应中提取指定字段
    
    Args:
        api_response: 实际控制人API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        包含提取字段的实际控制人数据列表
    """
    try:
        # 解析JSON字符串
        data = json.loads(api_response) if isinstance(api_response, str) else api_response
        
        # 检查响应结构
        if not data.get('result') or not data['result'].get('actualControllerList'):
            return []
        
        # 提取指定字段，限制数量
        extracted_controllers = []
        items = data['result']['actualControllerList'][:limit]  # 限制提取数量
        for item in items:
            # 处理ratio字段：转换为百分比格式
            ratio = item.get('ratio', 0)
            ratio_str = f"{ratio * 100:.1f}%" if isinstance(ratio, (int, float)) else str(ratio)
            
            controller_info = {
                'name': item.get('name', ''),
                'ratio': ratio_str
            }
            extracted_controllers.append(controller_info)
        
        return extracted_controllers
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"数据提取错误: {e}")
        return []


def extract_investment_data(api_response, limit: int = 20) -> List[Dict[str, str]]:
    """
    从投资数据API响应中提取指定字段
    
    Args:
        api_response: 投资数据API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        包含提取字段的投资数据列表
    """
    try:
        # 解析JSON字符串
        data = json.loads(api_response) if isinstance(api_response, str) else api_response
        
        # 检查响应结构
        if not data.get('result') or not data['result'].get('items'):
            return []
        
        # 提取指定字段，限制数量
        extracted_investments = []
        items = data['result']['items'][:limit]  # 限制提取数量
        for item in items:
            # 处理estiblishTime时间戳：除以1000后转换为日期格式
            establish_time = item.get('estiblishTime', '')
            date_str = ''
            if establish_time:
                try:
                    # 除以1000转换为秒，然后转换为日期
                    timestamp = int(establish_time) / 1000
                    date_str = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                except:
                    date_str = str(establish_time)
            
            investment_info = {
                'name': item.get('name', ''),
                'alias': item.get('alias', ''),
                'category': item.get('category', ''),
                'orgType': item.get('orgType', ''),
                'business_scope': item.get('business_scope', ''),
                'percent': item.get('percent', ''),
                'estiblishTime': date_str,
                'regStatus': item.get('regStatus', ''),
                'regCapital': item.get('regCapital', '')
            }
            extracted_investments.append(investment_info)
        
        return extracted_investments
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"数据提取错误: {e}")
        return []


def extract_company_data(api_response, limit: int = 20) -> Dict[str, Any]:
    """
    从公司基础信息API响应中提取所有字段（除代码字段外）
    
    Args:
        api_response: 公司基础信息API返回的JSON字符串或字典
        limit: 员工列表的最大数量，默认20
        
    Returns:
        包含提取字段的公司基础信息字典
    """
    try:
        # 解析JSON字符串
        data = json.loads(api_response) if isinstance(api_response, str) else api_response
        
        # 检查响应结构
        if not data.get('result'):
            return {}
        
        result = data['result']
        
        # 需要排除的代码相关字段
        exclude_fields = {
            'id', 'creditCode', 'regNumber', 'orgNumber', 'taxNumber', 'BRNNumber',
            'bondNum', 'type', 'percentileScore', 'isMicroEnt', 'base'
        }
        
        # 提取所有字段（除了排除的字段）
        company_info = {}
        
        for key, value in result.items():
            if key not in exclude_fields and value is not None:
                # 处理时间戳字段
                if key in ['updateTimes', 'fromTime', 'approvedTime', 'estiblishTime'] and value:
                    try:
                        timestamp = int(value) / 1000
                        company_info[key] = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                    except:
                        company_info[key] = str(value)
                # 处理列表字段
                elif key == 'historyNameList':
                    company_info[key] = '；'.join(value) if isinstance(value, list) else str(value)
                elif key == 'emailList':
                    company_info[key] = '；'.join(value) if isinstance(value, list) else str(value)
                # 处理员工信息，限制数量
                elif key == 'staffList':
                    if isinstance(value, dict) and 'result' in value:
                        staff_info = []
                        staff_items = value['result'][:limit]  # 限制员工数量
                        for staff in staff_items:
                            name = staff.get('name', '')
                            positions = staff.get('typeJoin', [])
                            position_str = '、'.join(positions) if isinstance(positions, list) else str(positions)
                            staff_info.append(f"{name}（{position_str}）")
                        company_info[key] = '；'.join(staff_info)
                    else:
                        company_info[key] = str(value)
                # 处理行业信息
                elif key == 'industryAll':
                    if isinstance(value, dict):
                        industry_parts = []
                        for industry_key in ['category', 'categoryBig', 'categoryMiddle', 'categorySmall']:
                            if value.get(industry_key):
                                industry_parts.append(value[industry_key])
                        company_info[key] = ' > '.join(industry_parts)
                    else:
                        company_info[key] = str(value)
                # 处理其他字段
                else:
                    company_info[key] = str(value) if value else ''
        
        return company_info
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return {}
    except Exception as e:
        print(f"数据提取错误: {e}")
        return {}


def extract_development_data(api_response, limit: int = 20) -> Dict[str, List[Dict[str, str]]]:
    """
    从发展信息API响应中提取各类数据
    
    Args:
        api_response: 发展信息API返回的JSON字符串或字典
        limit: 每个列表的最大数量，默认20
        
    Returns:
        包含各类提取字段的数据字典
    """
    try:
        # 解析JSON字符串
        data = json.loads(api_response) if isinstance(api_response, str) else api_response
        
        # 检查响应结构
        if not data.get('result'):
            return {}
        
        result = data['result']
        extracted_data = {}
        
        # 提取私募基金信息 (privateFundList)
        if 'privateFundList' in result:
            private_funds = []
            items = result['privateFundList'][:limit]  # 限制提取数量
            for item in items:
                fund_info = {
                    'org_type': item.get('org_type', ''),
                    'est_date': item.get('est_date', ''),
                    'alias': item.get('alias', '')
                }
                private_funds.append(fund_info)
            extracted_data['privateFunds'] = private_funds
        
        # 提取投资事件信息 (tzList)
        if 'tzList' in result:
            investments = []
            items = result['tzList'][:limit]  # 限制提取数量
            for item in items:
                investment_info = {
                    'product': item.get('product', ''),
                    'company': item.get('company', ''),
                    'hangye1': item.get('hangye1', ''),
                    'yewu': item.get('yewu', ''),
                    'location': item.get('location', ''),
                    'tzdate': item.get('tzdate', ''),
                    'lunci': item.get('lunci', ''),
                    'rongzi_map': item.get('rongzi_map', '')
                }
                investments.append(investment_info)
            extracted_data['investments'] = investments
        
        # 提取核心团队信息 (teamList)
        if 'teamList' in result:
            teams = []
            items = result['teamList'][:limit]  # 限制提取数量
            for item in items:
                team_info = {
                    'name': item.get('name', ''),
                    'title': item.get('title', ''),
                    'isDimission': str(item.get('isDimission', '')),
                    'desc': item.get('desc', ''),
                    'companyName': item.get('companyName', '')
                }
                teams.append(team_info)
            extracted_data['teams'] = teams
        
        # 提取投资机构信息 (investOrgList)
        if 'investOrgList' in result:
            invest_orgs = []
            items = result['investOrgList'][:limit]  # 限制提取数量
            for item in items:
                org_info = {
                    'jigou_name': item.get('jigou_name', ''),
                    'area': item.get('area', ''),
                    'desc': item.get('desc', ''),
                    'foundYear': item.get('foundYear', '')
                }
                invest_orgs.append(org_info)
            extracted_data['investOrgs'] = invest_orgs
        
        # 提取融资历史信息 (rongziList)
        if 'rongziList' in result:
            financings = []
            items = result['rongziList'][:limit]  # 限制提取数量
            for item in items:
                # 处理时间戳
                pub_time = item.get('pubTime', '')
                if pub_time and str(pub_time).isdigit():
                    try:
                        pub_time = datetime.datetime.fromtimestamp(int(pub_time) / 1000).strftime('%Y-%m-%d')
                    except:
                        pass
                
                financing_info = {
                    'date': item.get('date', ''),
                    'pubTime': str(pub_time),
                    'investorName': item.get('investorName', ''),
                    'money': item.get('money', ''),
                    'newsTitle': item.get('newsTitle', ''),
                    'round': item.get('round', ''),
                    'share': item.get('share', ''),
                    'value': item.get('value', '')
                }
                financings.append(financing_info)
            extracted_data['financings'] = financings
        
        return extracted_data
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return {}
    except Exception as e:
        print(f"数据提取错误: {e}")
        return {}


def development_data_to_markdown(development_data: Dict[str, List[Dict[str, str]]], title: str = "企业发展信息") -> str:
    """
    将发展信息数据转换为多层级Markdown格式
    
    Args:
        development_data: 发展信息数据字典
        title: 主标题
        
    Returns:
        多层级Markdown格式的字符串
    """
    if not development_data:
        return f"# {title}\n\n暂无数据"
    
    # 各类数据的中文标签
    section_labels = {
        'privateFunds': '私募基金',
        'investments': '投资事件',
        'teams': '核心团队',
        'investOrgs': '投资机构',
        'financings': '融资历史',
        'products': '产品列表'
    }
    
    field_labels = {
        # 私募基金字段
        'org_type': '机构类型',
        'est_date': '成立日期',
        'alias': '别名',
        
        # 投资事件字段
        'product': '产品名称',
        'company': '公司名称',
        'hangye1': '行业',
        'yewu': '业务描述',
        'location': '地区',
        'tzdate': '投资日期',
        'lunci': '轮次',
        'rongzi_map': '融资映射',
        
        # 核心团队字段
        'name': '姓名',
        'title': '职位',
        'isDimission': '是否离职',
        'companyName': '公司名称',
        
        # 投资机构字段
        'jigou_name': '机构名称',
        'area': '地区',
        'foundYear': '成立年份',
        
        # 融资历史字段
        'date': '日期',
        'pubTime': '发布时间',
        'investorName': '投资方',
        'money': '融资金额',
        'newsTitle': '新闻标题',
        'round': '融资轮次',
        'share': '股份',
        'value': '估值',
        
        # 产品列表字段
        'product': '产品名称',
        'setupDate': '成立日期',
        'yewu': '业务描述',
        'companyName': '公司名称',
        'hangye': '行业'
    }
    
    markdown_content = f"# {title}\n\n"
    
    # 为每个数据类别创建一级标题
    for section_key, section_data in development_data.items():
        if not section_data:
            continue
            
        section_title = section_labels.get(section_key, section_key)
        markdown_content += f"## {section_title}\n\n"
        
        # 为每个数据项创建二级标题
        for i, item in enumerate(section_data, 1):
            # 使用合适的字段作为标题
            if section_key == 'teams':
                item_title = item.get('name', f'成员 {i}')
            elif section_key == 'investments':
                item_title = item.get('product', f'投资项目 {i}')
            elif section_key == 'financings':
                round_info = item.get('round', '')
                item_title = f"{round_info}" if round_info else f"融资事件 {i}"
            elif section_key == 'investOrgs':
                item_title = item.get('jigou_name', f'投资机构 {i}')
            elif section_key == 'products':
                item_title = item.get('product', f'产品 {i}')
            elif section_key == 'privateFunds':
                item_title = item.get('alias', f'基金 {i}')
            else:
                item_title = f'项目 {i}'
                
            markdown_content += f"### {i}. {item_title}\n\n"
            
            # 为每个字段创建列表项
            for key, value in item.items():
                if value:  # 只显示有值的字段
                    # 动态处理desc字段的标签
                    if key == 'desc':
                        if section_key == 'teams':
                            label = '个人简介'
                        elif section_key == 'investOrgs':
                            label = '机构简介'
                        else:
                            label = '描述'
                    else:
                        label = field_labels.get(key, key)
                    
                    # 特殊处理
                    if key == 'isDimission':
                        value = '是' if value == '1' else '否' if value == '0' else value
                    
                    # 对于较长的文本进行特殊处理
                    if key in ['desc'] and len(str(value)) > 100:
                        markdown_content += f"- **{label}**:\n\n  {value}\n\n"
                    else:
                        markdown_content += f"- **{label}**: {value}\n"
            
            markdown_content += "\n"
        
        markdown_content += "---\n\n"
    
    return markdown_content


def data_to_markdown(data_list: List[Dict[str, Any]], title: str = "数据信息", 
                     field_labels: Dict[str, str] = None) -> str:
    """
    将数据列表转换为多层级Markdown格式（更通用的函数）
    
    Args:
        data_list: 数据字典列表
        title: 主标题
        field_labels: 字段标签映射，如 {'title': '专利名称', 'cat': '分类'}
        
    Returns:
        多层级Markdown格式的字符串
    """
    if not data_list:
        return f"# {title}\n\n暂无数据"
    
    # 默认字段标签
    default_labels = {
        'title': '标题',
        'cat': '分类', 
        'patentType': '专利类型',
        'patentStatus': '专利状态',
        'abstracts': '摘要',
        'applicantname': '申请人',
        'pubDate': '公布日期',
        'applicationPublishTime': '申请公布时间',
        'name': '产品名称',
        'filterName': '筛选名称',
        'type': '类型',
        'brief': '简介',
        'classes': '分类',
        'website': '来源网站',
        'tags': '标签',
        'rtm': '发布日期',
        'ratio': '控制比例',
        'alias': '简称',
        'category': '行业分类',
        'orgType': '组织类型',
        'business_scope': '经营范围',
        'percent': '持股比例',
        'estiblishTime': '成立时间',
        'regStatus': '注册状态',
        'regCapital': '注册资本'
    }
    
    # 合并用户提供的标签
    labels = {**default_labels, **(field_labels or {})}
    
    markdown_content = f"# {title}\n\n"
    
    # 为每个数据项创建二级标题
    for i, item in enumerate(data_list, 1):
        # 使用name或title作为二级标题，如果都没有则使用序号
        item_title = item.get('name') or item.get('title', f'项目 {i}')
        markdown_content += f"## {i}. {item_title}\n\n"
        
        # 为每个字段创建列表项
        for key, value in item.items():
            if value:  # 只显示有值的字段
                # 跳过title字段，因为已经作为标题显示了
                if key == 'title':
                    continue
                    
                label = labels.get(key, key)
                # 对于较长的文本（如简介、摘要），进行特殊处理
                if key in ['brief', 'abstracts'] and len(value) > 100:
                    # 将长文本分段显示
                    markdown_content += f"- **{label}**:\n\n  {value}\n\n"
                else:
                    markdown_content += f"- **{label}**: {value}\n"
        
        markdown_content += "\n"
    
    return markdown_content


def patents_to_markdown(patents: List[Dict[str, str]]) -> str:
    """
    将专利数据转换为多层级Markdown格式
    
    Args:
        patents: 专利数据列表
        
    Returns:
        多层级Markdown格式的字符串
    """
    # 专利字段的中文标签
    patent_labels = {
        'title': '专利名称',
        'cat': '技术分类',
        'patentType': '专利类型', 
        'patentStatus': '专利状态',
        'abstracts': '摘要',
        'applicantname': '申请人',
        'pubDate': '公布日期',
        'applicationPublishTime': '申请公布时间'
    }
    
    return data_to_markdown(patents, "专利信息", patent_labels)


def products_to_markdown(products: List[Dict[str, str]]) -> str:
    """
    将产品数据转换为多层级Markdown格式
    
    Args:
        products: 产品数据列表
        
    Returns:
        多层级Markdown格式的字符串
    """
    # 产品字段的中文标签
    product_labels = {
        'name': '产品名称',
        'filterName': '筛选名称',
        'type': '产品类型',
        'brief': '产品简介',
        'classes': '产品分类'
    }
    
    return data_to_markdown(products, "产品信息", product_labels)


def news_to_markdown(news: List[Dict[str, str]]) -> str:
    """
    将新闻数据转换为多层级Markdown格式
    
    Args:
        news: 新闻数据列表
        
    Returns:
        多层级Markdown格式的字符串
    """
    # 新闻字段的中文标签
    news_labels = {
        'title': '新闻标题',
        'abstracts': '新闻摘要',
        'website': '来源网站',
        'tags': '标签',
        'rtm': '发布日期'
    }
    
    return data_to_markdown(news, "新闻信息", news_labels)


def controllers_to_markdown(controllers: List[Dict[str, str]]) -> str:
    """
    将实际控制人数据转换为多层级Markdown格式
    
    Args:
        controllers: 实际控制人数据列表
        
    Returns:
        多层级Markdown格式的字符串
    """
    # 实际控制人字段的中文标签
    controller_labels = {
        'name': '控制人姓名',
        'ratio': '控制比例'
    }
    
    return data_to_markdown(controllers, "实际控制人信息", controller_labels)


def investments_to_markdown(investments: List[Dict[str, str]]) -> str:
    """
    将投资数据转换为多层级Markdown格式
    
    Args:
        investments: 投资数据列表
        
    Returns:
        多层级Markdown格式的字符串
    """
    # 投资数据字段的中文标签
    investment_labels = {
        'name': '公司名称',
        'alias': '公司简称',
        'category': '行业分类',
        'orgType': '组织类型',
        'business_scope': '经营范围',
        'percent': '持股比例',
        'estiblishTime': '成立时间',
        'regStatus': '注册状态',
        'regCapital': '注册资本'
    }
    
    return data_to_markdown(investments, "投资信息", investment_labels)


def company_to_markdown(company_info: Dict[str, Any]) -> str:
    """
    将公司基础信息转换为Markdown格式
    
    Args:
        company_info: 公司基础信息字典
        
    Returns:
        Markdown格式的字符串
    """
    if not company_info:
        return "# 公司基础信息\n\n暂无数据"
    
    # 公司信息字段的中文标签
    company_labels = {
        'name': '公司名称',
        'alias': '公司简称',
        'historyNames': '历史名称',
        'historyNameList': '历史名称列表',
        'regStatus': '注册状态',
        'regCapital': '注册资本',
        'actualCapital': '实缴资本',
        'regCapitalCurrency': '注册资本币种',
        'actualCapitalCurrency': '实缴资本币种',
        'city': '所在城市',
        'district': '所在区县',
        'regLocation': '注册地址',
        'staffNumRange': '员工规模',
        'socialStaffNum': '社保人数',
        'industry': '所属行业',
        'industryAll': '详细行业分类',
        'bondName': '股票简称',
        'bondType': '股票类型',
        'usedBondName': '曾用股票名称',
        'legalPersonName': '法定代表人',
        'companyOrgType': '公司类型',
        'estiblishTime': '成立时间',
        'fromTime': '营业期限开始',
        'toTime': '营业期限结束',
        'approvedTime': '核准时间',
        'updateTimes': '更新时间',
        'cancelDate': '注销日期',
        'cancelReason': '注销原因',
        'revokeDate': '吊销日期',
        'revokeReason': '吊销原因',
        'regInstitute': '登记机关',
        'businessScope': '经营范围',
        'staffList': '主要人员',
        'email': '邮箱',
        'emailList': '邮箱列表',
        'phoneNumber': '联系电话',
        'websiteList': '官方网站',
        'tags': '企业标签',
        'property3': '英文名称'
    }
    
    # 获取公司名称作为标题
    company_name = company_info.get('name', '公司基础信息')
    markdown_content = f"# {company_name}\n\n"
    
    # 按类别组织信息
    sections = {
        '基本信息': ['name', 'alias', 'property3', 'legalPersonName', 'companyOrgType', 'regStatus'],
        '注册信息': ['regCapital', 'actualCapital', 'regCapitalCurrency', 'actualCapitalCurrency', 'estiblishTime', 'fromTime', 'toTime', 'approvedTime', 'regInstitute', 'regLocation'],
        '经营信息': ['industry', 'industryAll', 'businessScope'],
        '规模信息': ['staffNumRange', 'socialStaffNum', 'staffList'],
        '股票信息': ['bondName', 'bondType', 'usedBondName'],
        '联系信息': ['city', 'district', 'phoneNumber', 'email', 'emailList', 'websiteList'],
        '历史信息': ['historyNames', 'historyNameList', 'updateTimes'],
        '其他信息': ['tags', 'cancelDate', 'cancelReason', 'revokeDate', 'revokeReason']
    }
    
    for section_name, fields in sections.items():
        section_content = []
        for field in fields:
            if field in company_info and company_info[field]:
                label = company_labels.get(field, field)
                value = company_info[field]
                
                # 对于较长的文本进行特殊处理
                if field in ['businessScope', 'staffList'] and len(str(value)) > 100:
                    section_content.append(f"- **{label}**:\n\n  {value}\n")
                else:
                    section_content.append(f"- **{label}**: {value}")
        
        if section_content:
            markdown_content += f"## {section_name}\n\n"
            markdown_content += '\n'.join(section_content)
            markdown_content += "\n\n"
    
    return markdown_content


def process_patent_api_response(api_response: str, limit: int = 20) -> str:
    """
    处理专利API响应的完整流程：提取数据并转换为Markdown
    
    Args:
        api_response: 专利API返回的JSON字符串
        limit: 提取数据的最大数量，默认20
        
    Returns:
        Markdown格式的专利信息
    """
    # 提取专利数据
    patents = extract_patent_data(api_response, limit)
    
    # 转换为Markdown
    markdown_result = patents_to_markdown(patents)
    
    return markdown_result


def process_product_api_response(api_response, limit: int = 20) -> str:
    """
    处理产品API响应的完整流程：提取数据并转换为Markdown
    
    Args:
        api_response: 产品API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        Markdown格式的产品信息
    """
    # 提取产品数据
    products = extract_product_data(api_response, limit)
    
    # 转换为Markdown
    markdown_result = products_to_markdown(products)
    
    return markdown_result


def process_news_api_response(api_response, limit: int = 20) -> str:
    """
    处理新闻API响应的完整流程：提取数据并转换为Markdown
    
    Args:
        api_response: 新闻API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        Markdown格式的新闻信息
    """
    # 提取新闻数据
    news = extract_news_data(api_response, limit)
    
    # 转换为Markdown
    markdown_result = news_to_markdown(news)
    
    return markdown_result


def process_controller_api_response(api_response, limit: int = 20) -> str:
    """
    处理实际控制人API响应的完整流程：提取数据并转换为Markdown
    
    Args:
        api_response: 实际控制人API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        Markdown格式的实际控制人信息
    """
    # 提取实际控制人数据
    controllers = extract_controller_data(api_response, limit)
    
    # 转换为Markdown
    markdown_result = controllers_to_markdown(controllers)
    
    return markdown_result


def process_investment_api_response(api_response, limit: int = 20) -> str:
    """
    处理投资数据API响应的完整流程：提取数据并转换为Markdown
    
    Args:
        api_response: 投资数据API返回的JSON字符串或字典
        limit: 提取数据的最大数量，默认20
        
    Returns:
        Markdown格式的投资信息
    """
    # 提取投资数据
    investments = extract_investment_data(api_response, limit)
    
    # 转换为Markdown
    markdown_result = investments_to_markdown(investments)
    
    return markdown_result


def process_development_api_response(api_response, limit: int = 20) -> str:
    """
    处理发展信息API响应的完整流程：提取数据并转换为Markdown
    
    Args:
        api_response: 发展信息API返回的JSON字符串或字典
        limit: 每个列表的最大数量，默认20
        
    Returns:
        Markdown格式的发展信息
    """
    # 提取发展信息数据
    development_data = extract_development_data(api_response, limit)
    
    # 转换为Markdown
    markdown_result = development_data_to_markdown(development_data)
    
    return markdown_result


def process_company_api_response(api_response, limit: int = 20) -> str:
    """
    处理公司基础信息API响应的完整流程：提取数据并转换为Markdown
    
    Args:
        api_response: 公司基础信息API返回的JSON字符串或字典
        limit: 员工列表的最大数量，默认20
        
    Returns:
        Markdown格式的公司基础信息
    """
    # 提取公司基础信息数据
    company_info = extract_company_data(api_response, limit)
    
    # 转换为Markdown
    markdown_result = company_to_markdown(company_info)
    
    return markdown_result


# 示例使用
if __name__ == "__main__":
    # 示例公司基础信息API响应数据
    sample_company_response = {
        "result": {
            "historyNames": "贵州力源液压股份有限公司;",
            "regStatus": "存续",
            "cancelDate": None,
            "regCapital": "77800.32万人民币",
            "city": "贵阳市",
            "staffNumRange": "5000-9999人",
            "industry": "汽车制造业",
            "historyNameList": [
                "贵州力源液压股份有限公司"
            ],
            "bondNum": "600765",
            "type": 1,
            "bondName": "中航重机",
            "updateTimes": 1627952558000,
            "revokeDate": None,
            "staffList": {
                "result": [
                    {
                        "name": "姬苏春",
                        "id": 1875401345,
                        "type": 2,
                        "typeJoin": [
                            "董事长"
                        ]
                    },
                    {
                        "name": "冉兴",
                        "id": 1799186709,
                        "type": 2,
                        "typeJoin": [
                            "董事兼总经理"
                        ]
                    },
                    {
                        "name": "李平",
                        "id": 2353584340,
                        "type": 2,
                        "typeJoin": [
                            "董事"
                        ]
                    }
                ],
                "total": 11
            },
            "legalPersonName": "姬苏春",
            "revokeReason": "",
            "regNumber": "520000000005018",
            "property3": "AVIC Heavy Machinery Co.,Ltd.",
            "creditCode": "91520000*********R",
            "usedBondName": "力源液压->G力源->力源液压",
            "fromTime": 847900800000,
            "approvedTime": 1582646400000,
            "socialStaffNum": 8884,
            "alias": "中航重机",
            "companyOrgType": "其他股份有限公司(上市)",
            "actualCapitalCurrency": "人民币",
            "id": 11684584,
            "orgNumber": "*********",
            "cancelReason": "",
            "toTime": None,
            "email": "<EMAIL>",
            "emailList": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "actualCapital": "-",
            "estiblishTime": 847900800000,
            "regInstitute": "贵阳市市场监督管理局贵州双龙航空港经济区分局",
            "taxNumber": "91520000*********R",
            "businessScope": "法律、法规、国务院决定规定禁止的不得经营；法律、法规、国务院决定规定应当许可（审批）的，经审批机关批准后凭许可（审批）文件经营;法律、法规、国务院决定规定无需许可（审批）的，市场主体自主选择经营。（股权投资及经营管理；军民共用液压件、液压系统、锻件、铸件、换热器、飞机及航空发动机附件，汽车零备件的研制、开发、制造、修理及销售；经营本企业自产机电产品、成套设备及相关技术的出口业务；经营本企业生产、科研所需的原辅材料、机械设备、仪器仪表、备品备件、零配件及技术的进口业务；开展本企业进料加工和三来一补业务。液压、锻件、铸件、换热器技术开发、转让和咨询服务；物流；机械冷热加工、修理修配服务。）",
            "regLocation": "贵州双龙航空港经济区机场路9号太升国际A栋3单元5层",
            "regCapitalCurrency": "人民币",
            "tags": "存续;曾用名;定向增发;A股 | 中航重机 600765;项目品牌:中航重机;投资机构:中航重机;企业集团",
            "websiteList": "www.hm.avic.com",
            "phoneNumber": "0851-88600765",
            "district": "南明区",
            "name": "中航重机股份有限公司",
            "bondType": "A股",
            "percentileScore": 9696,
            "industryAll": {
                "categoryMiddle": "改装汽车制造",
                "categoryBig": "汽车制造业",
                "category": "制造业",
                "categorySmall": ""
            },
            "isMicroEnt": 0,
            "base": "gz",
            "BRNNumber": ""
        },
        "reason": "ok",
        "error_code": 0
    }
    
    # 测试公司基础信息函数
    print("=== 公司基础信息 ===")
    company_result = process_company_api_response(sample_company_response)
    print(company_result) 