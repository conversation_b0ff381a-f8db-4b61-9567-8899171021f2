"""
日志工具模块
提供统一的日志配置和request_id追踪功能
"""
import logging
import uuid
import contextvars
from typing import Optional
from ..config.app_config import app_config


# 创建context变量来存储request_id
request_id_var: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar('request_id', default=None)


class RequestIdFilter(logging.Filter):
    """为日志记录添加request_id的过滤器"""
    
    def filter(self, record):
        """为日志记录添加request_id字段"""
        record.request_id = request_id_var.get() or 'NO_REQUEST_ID'
        return True


def generate_request_id() -> str:
    """生成唯一的请求ID"""
    return str(uuid.uuid4())[:8]  # 使用UUID的前8位作为request_id


def set_request_id(request_id: Optional[str] = None) -> str:
    """设置当前请求的request_id"""
    if request_id is None:
        request_id = generate_request_id()
    
    request_id_var.set(request_id)
    return request_id


def get_request_id() -> Optional[str]:
    """获取当前请求的request_id"""
    return request_id_var.get()


def setup_logging() -> None:
    """设置全局日志配置"""
    # 获取日志配置
    log_config = app_config.logging
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_config.level))
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_config.level))
    
    # 创建格式化器
    formatter = logging.Formatter(
        fmt=log_config.format,
        datefmt=log_config.date_format
    )
    console_handler.setFormatter(formatter)
    
    # 添加request_id过滤器
    request_id_filter = RequestIdFilter()
    console_handler.addFilter(request_id_filter)
    
    # 将处理器添加到根日志记录器
    root_logger.addHandler(console_handler)


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志记录器"""
    return logging.getLogger(name)


class RequestContext:
    """请求上下文管理器，自动管理request_id的生命周期"""
    
    def __init__(self, request_id: Optional[str] = None):
        self.request_id = request_id
        self.old_request_id = None
    
    def __enter__(self) -> str:
        """进入上下文时设置request_id"""
        self.old_request_id = get_request_id()
        self.request_id = set_request_id(self.request_id)
        return self.request_id
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文时恢复原来的request_id"""
        request_id_var.set(self.old_request_id)


# 初始化日志系统
setup_logging()
