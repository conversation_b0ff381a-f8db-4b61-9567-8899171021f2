"""
重构后的主入口文件
简化的命令行界面，主要作为项目的启动器和协调者
"""
import asyncio
import argparse
import json
from typing import Optional

from .config.app_config import app_config
from .core.entity_processor import EntityProcessor
from .utils.logging_utils import get_logger, RequestContext

logger = get_logger(__name__)


class CommandLineInterface:
    """命令行界面类，负责参数解析和命令执行"""
    
    def __init__(self):
        self.processor: Optional[EntityProcessor] = None
    
    def create_argument_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="实体一句话总结处理工具 (重构版)",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  %(prog)s --company-id 2353364615                    # 处理指定公司
  %(prog)s --company-id 2353364615 --no-api-cache     # 禁用API缓存
  %(prog)s --company-id 2353364615 --limit 10         # 限制每个API最多10条数据
  %(prog)s --info 2353364615                          # 查看公司信息
  %(prog)s --list-config                              # 显示当前配置
            """
        )
        
        # 主要操作参数
        parser.add_argument("--company-id", type=str, help="要处理的公司ID")
        parser.add_argument("--info", type=str, help="查看特定公司的详细信息")
        parser.add_argument("--list-config", action="store_true", help="显示当前配置")
        
        # 处理参数
        parser.add_argument("--limit", type=int, default=20, help="每个API数据的最大条目数 (默认: 20)")
        parser.add_argument("--no-save", action="store_true", help="不保存结果到缓存")
        
        # 缓存控制参数
        cache_group = parser.add_argument_group('缓存控制', '控制不同层级的缓存使用')
        cache_group.add_argument("--no-api-cache", action="store_true", help="禁用API缓存")
        cache_group.add_argument("--no-homepage-cache", action="store_true", help="禁用主页缓存")
        cache_group.add_argument("--no-llm-cache", action="store_true", help="禁用LLM缓存")
        cache_group.add_argument("--no-final-cache", action="store_true", help="禁用最终结果缓存")
        
        return parser
    
    def get_cache_overrides(self, args) -> dict:
        """根据命令行参数获取缓存配置覆盖"""
        overrides = {}
        
        if args.no_api_cache:
            overrides['use_api_cache'] = False
        if args.no_homepage_cache:
            overrides['use_homepage_cache'] = False
        if args.no_llm_cache:
            overrides['use_llm_cache'] = False
        if args.no_final_cache:
            overrides['use_final_result_cache'] = False
        
        return overrides
    
    def print_cache_config(self, processor: EntityProcessor):
        """打印当前缓存配置"""
        cache_config = processor.cache_manager.cache_config
        
        print("=== 当前缓存配置 ===")
        print(f"API缓存:      {'启用' if cache_config.use_api_cache else '禁用'}")
        print(f"主页缓存:     {'启用' if cache_config.use_homepage_cache else '禁用'}")
        print(f"LLM缓存:      {'启用' if cache_config.use_llm_cache else '禁用'}")
        print(f"最终结果缓存: {'启用' if cache_config.use_final_result_cache else '禁用'}")
        print("=" * 20)
    
    async def handle_company_processing(self, args, processor: EntityProcessor):
        """处理公司信息"""
        company_id = args.company_id
        
        with RequestContext() as request_id:
            logger.info(f"开始处理公司请求: company_id={company_id}, request_id={request_id}")
            
            self.print_cache_config(processor)
            
            try:
                result = await processor.process_company(
                    company_id=company_id,
                    limit=args.limit,
                    save_to_cache=not args.no_save
                )
                
                # 输出结果
                print(f"\n=== 处理结果 ===")
                print(f"公司ID: {result['company_id']}")
                print(f"请求ID: {result['request_id']}")
                print(f"来源: {'缓存' if result['from_cache'] else '实时处理'}")
                
                if not result['from_cache']:
                    details = result['processing_details']
                    print(f"API数据长度: {details['api_data_length']}")
                    print(f"格式化内容长度: {details['formatted_content_length']}")
                    print(f"主页数据: {'有' if details['homepage_data_available'] else '无'}")
                    print(f"一句话总结数量: {details['one_liners_count']}")
                    print(f"关键词数量: {details['keywords_count']}")
                
                # 输出最终结果
                final_result = result['final_result']
                print(f"\n=== 最终结果 ===")
                print(f"一句话总结: {final_result.get('one_liner', '无')}")
                print(f"关键词数量: {len(final_result.get('keywords', []))}")
                
                # 输出JSON格式结果
                print(f"\n=== JSON格式结果 ===")
                print(json.dumps(final_result, ensure_ascii=False, indent=2))
                
            except Exception as e:
                logger.error(f"处理公司失败: {e}")
                print(f"错误: {e}")
    
    async def handle_company_info(self, args, processor: EntityProcessor):
        """查看公司信息"""
        company_id = args.info
        
        with RequestContext() as request_id:
            logger.info(f"查看公司信息: company_id={company_id}, request_id={request_id}")
            
            try:
                info = await processor.get_company_info(company_id)
                
                if info:
                    print(f"=== 公司信息 ===")
                    print(f"公司ID: {info['company_id']}")
                    print(f"请求ID: {info['request_id']}")
                    
                    final_result = info['final_result']
                    print(f"一句话总结: {final_result.get('one_liner', '无')}")
                    print(f"关键词数量: {len(final_result.get('keywords', []))}")
                    
                    print(f"\n=== JSON格式结果 ===")
                    print(json.dumps(final_result, ensure_ascii=False, indent=2))
                else:
                    print(f"未找到公司信息: {company_id}")
                    
            except Exception as e:
                logger.error(f"查看公司信息失败: {e}")
                print(f"错误: {e}")
    
    def handle_list_config(self):
        """显示当前配置"""
        print("=== 应用配置 ===")
        print(f"最大工作线程: {app_config.max_workers}")
        print(f"请求超时: {app_config.request_timeout}秒")
        print(f"重试次数: {app_config.retry_attempts}")
        
        print(f"\n=== 缓存配置 ===")
        cache_config = app_config.cache
        print(f"API缓存: {'启用' if cache_config.use_api_cache else '禁用'}")
        print(f"主页缓存: {'启用' if cache_config.use_homepage_cache else '禁用'}")
        print(f"LLM缓存: {'启用' if cache_config.use_llm_cache else '禁用'}")
        print(f"最终结果缓存: {'启用' if cache_config.use_final_result_cache else '禁用'}")
        
        print(f"\n=== 日志配置 ===")
        log_config = app_config.logging
        print(f"日志级别: {log_config.level}")
        print(f"日志格式: {log_config.format}")
    
    async def run(self):
        """运行命令行界面"""
        parser = self.create_argument_parser()
        args = parser.parse_args()
        
        try:
            # 根据命令行参数创建处理器
            cache_overrides = self.get_cache_overrides(args)
            if cache_overrides:
                self.processor = EntityProcessor(app_config).override_cache_config(**cache_overrides)
            else:
                self.processor = EntityProcessor(app_config)
            
            # 执行相应的操作
            if args.company_id:
                await self.handle_company_processing(args, self.processor)
            elif args.info:
                await self.handle_company_info(args, self.processor)
            elif args.list_config:
                self.handle_list_config()
            else:
                parser.print_help()
                
        finally:
            if self.processor:
                await self.processor.close()


async def main():
    """主函数"""
    cli = CommandLineInterface()
    await cli.run()


if __name__ == "__main__":
    asyncio.run(main())
