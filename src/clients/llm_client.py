import asyncio
import os
import json
import hashlib
from typing import List, Optional, Dict
from openai import AsyncOpenAI
from ..config.cache_config import get_cache_instance


DASH_SCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY")


class LLMClient:
    def __init__(self):
        """
        初始化LLM客户端
        """
        self.client = AsyncOpenAI(
            # api_key="7fb7a28d-f55d-41b7-82c1-e09ef5b8015c",
            # base_url="https://ark.cn-beijing.volces.com/api/v3/",
            # api_key="sk-39080be1cb0646deb07b7e8bb34ae9a3",
            # base_url="https://api.deepseek.com",
            base_url="https://openrouter.ai/api/v1",
            api_key="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"
        )
        # 延迟初始化缓存实例
        self._cache = None

    @property
    def cache(self):
        """延迟初始化缓存实例"""
        if self._cache is None:
            self._cache = get_cache_instance('timeline')
        return self._cache

    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
        
    async def close(self):
        """Close the client session."""
        if hasattr(self.client, 'close') and callable(self.client.close):
            await self.client.close()
    
    async def generate_response(self, user_prompt: str, use_cache: bool = True) -> str:
        """
        Generate a response from the LLM.
        """
        # 检查缓存
        if use_cache:
            # 对于长提示，使用哈希值作为缓存键
            prompt_hash = hashlib.md5(user_prompt.encode()).hexdigest()
            cache_key = f"llm_response:{prompt_hash}"
            
            cached_result = await self.cache.get(cache_key)
            if cached_result is not None:
                # 移除缓存输出信息，让它更安静
                return cached_result
        
        try:
            response = await self.client.chat.completions.create(
                # model="deepseek-r1-250120",
                model="deepseek-reasoner",
                messages=[
                    {"role": "user", "content": user_prompt},
                ],
            )   
            result = response.choices[0].message.content
            
            # 缓存结果
            if use_cache:
                await self.cache.set(cache_key, result)
                
            return result
        except Exception as e:
            # Log the error or handle it appropriately
            raise e

    async def generate_response_with_messages(self, messages: List[Dict[str, str]], use_cache: bool = True) -> str:
        """
        Generate a response from the LLM with messages.
        """
        try:
            response = await self.client.chat.completions.create(
                # model="deepseek-reasoner",
                # model="deepseek-r1-250120",
                model="google/gemini-2.5-pro-preview",
                messages=messages,
            )
            result = response.choices[0].message.content
            return result
        except Exception as e:
            raise e