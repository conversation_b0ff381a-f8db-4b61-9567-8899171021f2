import json
import os
import asyncio
import logging
import time
from datetime import datetime, timezone
from typing import Any, Dict, Optional, Tuple
from functools import wraps
import threading
from concurrent.futures import ThreadPoolExecutor

from sqlalchemy import Column, String, Text, DateTime, create_engine, MetaData, Table, select, insert, delete, update, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.exc import SQLAlchemyError, OperationalError

# 导入MySQL配置
from ..config.mysql_config import get_mysql_url, ENGINE_CONFIG

Base = declarative_base()
logger = logging.getLogger(__name__)

class CacheEntry(Base):
    __tablename__ = 'cache_entry'
    
    key = Column(String(255), primary_key=True)
    value = Column(Text)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    def __repr__(self):
        return f"<CacheEntry(key='{self.key}', created_at='{self.created_at}')>"


class AsyncMySQLCache:
    def __init__(self, table_name: str, max_retries: int = 3, retry_delay: float = 0.5):
        """
        初始化异步MySQL缓存
        
        Args:
            table_name: 表名
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
        """
        # 表名和重试配置
        self.table_name = table_name
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # 同步引擎，用于写入操作和表创建
        self.sync_engine = create_engine(
            get_mysql_url(async_driver=False),
            **ENGINE_CONFIG,
            connect_args={'charset': 'utf8mb4'}
        )
        
        # 异步引擎，用于读取操作
        self.async_engine = create_async_engine(
            get_mysql_url(async_driver=True),
            **ENGINE_CONFIG
        )
        
        # 创建会话
        self.sync_session = sessionmaker(bind=self.sync_engine)
        self.async_session = sessionmaker(
            self.async_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # 线程池用于执行同步写入操作
        self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="mysql_cache_writer")
        
        # 初始化表
        self._setup_table()
        
        # 线程锁，用于同步写入操作
        self._lock = threading.Lock()
        
        # 标记是否已关闭
        self._closed = False
        
    def _setup_table(self):
        """创建表结构"""
        try:
            # 使用原生SQL创建缓存表，确保兼容性
            with self.sync_engine.connect() as conn:
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    `key` VARCHAR(255) PRIMARY KEY,
                    `value` LONGTEXT,
                    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                conn.execute(text(create_table_sql))
                conn.commit()
                logger.info(f"MySQL缓存表 {self.table_name} 初始化完成")
        except Exception as e:
            logger.error(f"创建MySQL缓存表失败: {e}")
            
    def _sync_retry_operation(self, operation, *args, **kwargs):
        """
        同步重试数据库操作
        
        Args:
            operation: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                return operation(*args, **kwargs)
            except (OperationalError, SQLAlchemyError) as e:
                last_error = e
                error_str = str(e).lower()
                
                # MySQL常见的可重试错误
                retry_keywords = [
                    'connection', 'timeout', 'lost connection', 'gone away',
                    'deadlock', 'lock wait timeout', 'too many connections'
                ]
                
                if any(keyword in error_str for keyword in retry_keywords):
                    if attempt < self.max_retries:
                        logger.warning(f"MySQL操作失败（第 {attempt} 次尝试），准备重试: {e}")
                        time.sleep(self.retry_delay * attempt)  # 指数退避
                        continue
                    else:
                        logger.error(f"MySQL操作失败，所有 {self.max_retries} 次尝试均失败: {e}")
                        break
                else:
                    # 非重试类错误，直接抛出
                    logger.error(f"MySQL操作失败（非重试类错误）: {e}")
                    break
            except Exception as e:
                logger.error(f"MySQL操作发生未知错误: {e}")
                break
        
        return None

    async def _async_retry_operation(self, operation, *args, **kwargs):
        """
        异步重试数据库操作（用于读取）
        
        Args:
            operation: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                return await operation(*args, **kwargs)
            except (OperationalError, SQLAlchemyError) as e:
                last_error = e
                error_str = str(e).lower()
                
                # MySQL常见的可重试错误
                retry_keywords = [
                    'connection', 'timeout', 'lost connection', 'gone away',
                    'deadlock', 'lock wait timeout', 'too many connections'
                ]
                
                if any(keyword in error_str for keyword in retry_keywords):
                    if attempt < self.max_retries:
                        logger.warning(f"异步MySQL操作失败（第 {attempt} 次尝试），准备重试: {e}")
                        await asyncio.sleep(self.retry_delay * attempt)  # 异步睡眠
                        continue
                    else:
                        logger.error(f"异步MySQL操作失败，所有 {self.max_retries} 次尝试均失败: {e}")
                        break
                else:
                    # 非重试类错误，直接抛出
                    logger.error(f"异步MySQL操作失败（非重试类错误）: {e}")
                    break
            except Exception as e:
                logger.error(f"异步MySQL操作发生未知错误: {e}")
                break
        
        return None
        
    async def get(self, key: str) -> Optional[Any]:
        """
        异步获取缓存值（读取操作保持异步）
        
        Args:
            key: 缓存键
        
        Returns:
            缓存的值，如果不存在则返回None
        """
        async def _get_operation():
            async with self.async_session() as session:
                # 使用参数化查询
                query = text(f"SELECT `value` FROM {self.table_name} WHERE `key` = :key")
                result = await session.execute(query, {"key": key})
                row = result.fetchone()
                
                if row:
                    # 尝试解析JSON
                    try:
                        return json.loads(row[0])
                    except json.JSONDecodeError:
                        return row[0]
            return None
        
        return await self._async_retry_operation(_get_operation)
    
    def _sync_set(self, key: str, value_str: str) -> None:
        """
        同步设置缓存值
        """
        def _set_operation():
            with self._lock:
                with self.sync_session() as session:
                    # 使用MySQL的ON DUPLICATE KEY UPDATE语法
                    stmt = text(f"""
                    INSERT INTO {self.table_name} (`key`, `value`, `created_at`) 
                    VALUES (:key, :value, :created_at)
                    ON DUPLICATE KEY UPDATE 
                    `value` = VALUES(`value`), 
                    `created_at` = VALUES(`created_at`)
                    """)
                    params = {
                        "key": key, 
                        "value": value_str, 
                        "created_at": datetime.now(timezone.utc)
                    }
                    
                    session.execute(stmt, params)
                    session.commit()
        
        return self._sync_retry_operation(_set_operation)
    
    async def set(self, key: str, value: Any) -> None:
        """
        异步设置缓存值（写入操作在线程池中同步执行）
        
        Args:
            key: 缓存键
            value: 要缓存的值
        """
        # 如果值是字典或列表，转换为JSON字符串
        if isinstance(value, (dict, list)):
            value_str = json.dumps(value, ensure_ascii=False)
        else:
            value_str = str(value)
            
        # 在线程池中执行同步写入操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_set, key, value_str)
        except Exception as e:
            logger.warning(f"MySQL缓存写入失败，但不影响程序运行: {e}")
    
    def _sync_delete(self, key: str) -> None:
        """
        同步删除缓存
        """
        def _delete_operation():
            with self._lock:
                with self.sync_session() as session:
                    stmt = text(f"DELETE FROM {self.table_name} WHERE `key` = :key")
                    session.execute(stmt, {"key": key})
                    session.commit()
        
        return self._sync_retry_operation(_delete_operation)
        
    async def delete(self, key: str) -> None:
        """
        异步删除缓存（删除操作在线程池中同步执行）
        
        Args:
            key: 缓存键
        """
        # 在线程池中执行同步删除操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_delete, key)
        except Exception as e:
            logger.warning(f"MySQL缓存删除失败: {e}")
    
    def _sync_clear(self) -> None:
        """
        同步清空缓存表
        """
        def _clear_operation():
            with self._lock:
                with self.sync_session() as session:
                    stmt = text(f"DELETE FROM {self.table_name}")
                    session.execute(stmt)
                    session.commit()
        
        return self._sync_retry_operation(_clear_operation)
        
    async def clear(self) -> None:
        """异步清空缓存表（清空操作在线程池中同步执行）"""
        # 在线程池中执行同步清空操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_clear)
        except Exception as e:
            logger.warning(f"MySQL缓存清空失败: {e}")
    
    async def close(self):
        """正确关闭所有资源"""
        if self._closed:
            return
            
        try:
            # 关闭异步引擎
            if hasattr(self, 'async_engine') and self.async_engine:
                await self.async_engine.dispose()
            
            # 关闭同步引擎
            if hasattr(self, 'sync_engine') and self.sync_engine:
                self.sync_engine.dispose()
            
            # 关闭线程池
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=True)
                
            self._closed = True
        except Exception as e:
            logger.warning(f"关闭MySQL缓存资源时出错: {e}")
    
    def __del__(self):
        """析构函数，关闭线程池（避免异步资源清理错误）"""
        if self._closed:
            return
            
        try:
            # 只关闭线程池，避免在事件循环关闭后操作异步资源
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=False)
                
            # 同步引擎可以安全关闭
            if hasattr(self, 'sync_engine') and self.sync_engine:
                self.sync_engine.dispose()
        except Exception:
            pass


def async_mysql_cache_decorator(cache_instance: AsyncMySQLCache, key_func=None):
    """
    异步MySQL缓存装饰器
    
    Args:
        cache_instance: AsyncMySQLCache实例
        key_func: 自定义生成缓存键的函数，如果为None则使用函数参数生成键
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数组合生成键
                arg_str = str(args) + str(sorted(kwargs.items()))
                cache_key = f"{func.__name__}:{arg_str}"
            
            # 尝试从缓存获取
            try:
                cached_result = await cache_instance.get(cache_key)
                if cached_result is not None:
                    return cached_result
            except Exception as e:
                logger.warning(f"从MySQL缓存获取数据失败，继续执行原函数: {e}")
            
            # 缓存未命中，执行原函数
            result = await func(*args, **kwargs)
            
            # 尝试缓存结果
            try:
                await cache_instance.set(cache_key, result)
            except Exception as e:
                logger.warning(f"MySQL缓存结果失败，但不影响函数执行: {e}")
            
            return result
        
        return wrapper
    
    return decorator 

class AsyncMySQLApiCache:
    """
    基于API ID和Company ID联合主键的MySQL缓存
    专门用于OpenAPI调用结果的缓存
    """
    
    def __init__(self, table_name: str, max_retries: int = 3, retry_delay: float = 0.5):
        """
        初始化异步MySQL API缓存
        
        Args:
            table_name: 表名
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
        """
        # 表名和重试配置
        self.table_name = table_name
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # 同步引擎，用于写入操作和表创建
        self.sync_engine = create_engine(
            get_mysql_url(async_driver=False),
            **ENGINE_CONFIG,
            connect_args={'charset': 'utf8mb4'}
        )
        
        # 异步引擎，用于读取操作
        self.async_engine = create_async_engine(
            get_mysql_url(async_driver=True),
            **ENGINE_CONFIG
        )
        
        # 创建会话
        self.sync_session = sessionmaker(bind=self.sync_engine)
        self.async_session = sessionmaker(
            self.async_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # 线程池用于执行同步写入操作
        self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="mysql_api_cache_writer")
        
        # 初始化表
        self._setup_table()
        
        # 线程锁，用于同步写入操作
        self._lock = threading.Lock()
        
        # 标记是否已关闭
        self._closed = False
        
    def _setup_table(self):
        """创建API缓存表结构"""
        try:
            # 只创建表，不删除旧表，使用CREATE TABLE IF NOT EXISTS
            with self.sync_engine.connect() as conn:
                # 创建新的API缓存表，使用api_id和company_id作为联合主键
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    `api_id` VARCHAR(50) NOT NULL,
                    `company_id` VARCHAR(50) NOT NULL,
                    `value` LONGTEXT,
                    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`api_id`, `company_id`),
                    INDEX `idx_api_id` (`api_id`),
                    INDEX `idx_company_id` (`company_id`),
                    INDEX `idx_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                conn.execute(text(create_table_sql))
                conn.commit()
                logger.info(f"MySQL API缓存表 {self.table_name} 初始化完成")
        except Exception as e:
            logger.error(f"创建MySQL API缓存表失败: {e}")
    
    def drop_and_recreate_table(self):
        """手动删除并重新创建表（仅在需要时调用）"""
        try:
            # 先删除旧表
            with self.sync_engine.connect() as conn:
                drop_table_sql = f"DROP TABLE IF EXISTS {self.table_name}"
                conn.execute(text(drop_table_sql))
                conn.commit()
                logger.info(f"已删除旧的MySQL缓存表 {self.table_name}")
                
                # 创建新的API缓存表，使用api_id和company_id作为联合主键
                create_table_sql = f"""
                CREATE TABLE {self.table_name} (
                    `api_id` VARCHAR(50) NOT NULL,
                    `company_id` VARCHAR(50) NOT NULL,
                    `value` LONGTEXT,
                    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`api_id`, `company_id`),
                    INDEX `idx_api_id` (`api_id`),
                    INDEX `idx_company_id` (`company_id`),
                    INDEX `idx_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                conn.execute(text(create_table_sql))
                conn.commit()
                logger.info(f"新的MySQL API缓存表 {self.table_name} 创建完成")
        except Exception as e:
            logger.error(f"删除并重新创建MySQL API缓存表失败: {e}")

    def _sync_retry_operation(self, operation, *args, **kwargs):
        """
        同步重试数据库操作
        
        Args:
            operation: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                return operation(*args, **kwargs)
            except (OperationalError, SQLAlchemyError) as e:
                last_error = e
                error_str = str(e).lower()
                
                # MySQL常见的可重试错误
                retry_keywords = [
                    'connection', 'timeout', 'lost connection', 'gone away',
                    'deadlock', 'lock wait timeout', 'too many connections'
                ]
                
                if any(keyword in error_str for keyword in retry_keywords):
                    if attempt < self.max_retries:
                        logger.warning(f"MySQL API缓存操作失败（第 {attempt} 次尝试），准备重试: {e}")
                        time.sleep(self.retry_delay * attempt)  # 指数退避
                        continue
                    else:
                        logger.error(f"MySQL API缓存操作失败，所有 {self.max_retries} 次尝试均失败: {e}")
                        break
                else:
                    # 非重试类错误，直接抛出
                    logger.error(f"MySQL API缓存操作失败（非重试类错误）: {e}")
                    break
            except Exception as e:
                logger.error(f"MySQL API缓存操作发生未知错误: {e}")
                break
        
        return None

    async def _async_retry_operation(self, operation, *args, **kwargs):
        """
        异步重试数据库操作（用于读取）
        
        Args:
            operation: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                return await operation(*args, **kwargs)
            except (OperationalError, SQLAlchemyError) as e:
                last_error = e
                error_str = str(e).lower()
                
                # MySQL常见的可重试错误
                retry_keywords = [
                    'connection', 'timeout', 'lost connection', 'gone away',
                    'deadlock', 'lock wait timeout', 'too many connections'
                ]
                
                if any(keyword in error_str for keyword in retry_keywords):
                    if attempt < self.max_retries:
                        logger.warning(f"异步MySQL API缓存操作失败（第 {attempt} 次尝试），准备重试: {e}")
                        await asyncio.sleep(self.retry_delay * attempt)  # 异步睡眠
                        continue
                    else:
                        logger.error(f"异步MySQL API缓存操作失败，所有 {self.max_retries} 次尝试均失败: {e}")
                        break
                else:
                    # 非重试类错误，直接抛出
                    logger.error(f"异步MySQL API缓存操作失败（非重试类错误）: {e}")
                    break
            except Exception as e:
                logger.error(f"异步MySQL API缓存操作发生未知错误: {e}")
                break
        
        return None
        
    async def get(self, api_id: str, company_id: str) -> Optional[Any]:
        """
        异步获取缓存值（基于api_id和company_id）
        
        Args:
            api_id: API ID
            company_id: 公司ID
        
        Returns:
            缓存的值，如果不存在则返回None
        """
        async def _get_operation():
            async with self.async_session() as session:
                # 使用参数化查询
                query = text(f"SELECT `value` FROM {self.table_name} WHERE `api_id` = :api_id AND `company_id` = :company_id")
                result = await session.execute(query, {"api_id": api_id, "company_id": company_id})
                row = result.fetchone()
                
                if row:
                    # 尝试解析JSON
                    try:
                        return json.loads(row[0])
                    except json.JSONDecodeError:
                        return row[0]
            return None
        
        return await self._async_retry_operation(_get_operation)
    
    def _sync_set(self, api_id: str, company_id: str, value_str: str) -> None:
        """
        同步设置缓存值
        """
        def _set_operation():
            with self._lock:
                with self.sync_session() as session:
                    # 使用MySQL的ON DUPLICATE KEY UPDATE语法
                    stmt = text(f"""
                    INSERT INTO {self.table_name} (`api_id`, `company_id`, `value`, `created_at`) 
                    VALUES (:api_id, :company_id, :value, :created_at)
                    ON DUPLICATE KEY UPDATE 
                    `value` = VALUES(`value`), 
                    `created_at` = VALUES(`created_at`)
                    """)
                    params = {
                        "api_id": api_id,
                        "company_id": company_id,
                        "value": value_str, 
                        "created_at": datetime.now(timezone.utc)
                    }
                    
                    session.execute(stmt, params)
                    session.commit()
        
        return self._sync_retry_operation(_set_operation)
    
    async def set(self, api_id: str, company_id: str, value: Any) -> None:
        """
        异步设置缓存值（基于api_id和company_id）
        
        Args:
            api_id: API ID
            company_id: 公司ID
            value: 要缓存的值
        """
        # 如果值是字典或列表，转换为JSON字符串
        if isinstance(value, (dict, list)):
            value_str = json.dumps(value, ensure_ascii=False)
        else:
            value_str = str(value)
            
        # 在线程池中执行同步写入操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_set, api_id, company_id, value_str)
        except Exception as e:
            logger.warning(f"MySQL API缓存写入失败，但不影响程序运行: {e}")
    
    def _sync_delete(self, api_id: str, company_id: str) -> None:
        """
        同步删除缓存
        """
        def _delete_operation():
            with self._lock:
                with self.sync_session() as session:
                    stmt = text(f"DELETE FROM {self.table_name} WHERE `api_id` = :api_id AND `company_id` = :company_id")
                    session.execute(stmt, {"api_id": api_id, "company_id": company_id})
                    session.commit()
        
        return self._sync_retry_operation(_delete_operation)
        
    async def delete(self, api_id: str, company_id: str) -> None:
        """
        异步删除缓存（基于api_id和company_id）
        
        Args:
            api_id: API ID
            company_id: 公司ID
        """
        # 在线程池中执行同步删除操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_delete, api_id, company_id)
        except Exception as e:
            logger.warning(f"MySQL API缓存删除失败: {e}")
    
    def _sync_clear(self) -> None:
        """
        同步清空缓存表
        """
        def _clear_operation():
            with self._lock:
                with self.sync_session() as session:
                    stmt = text(f"DELETE FROM {self.table_name}")
                    session.execute(stmt)
                    session.commit()
        
        return self._sync_retry_operation(_clear_operation)
        
    async def clear(self) -> None:
        """异步清空缓存表（清空操作在线程池中同步执行）"""
        # 在线程池中执行同步清空操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_clear)
        except Exception as e:
            logger.warning(f"MySQL API缓存清空失败: {e}")
    
    async def close(self):
        """正确关闭所有资源"""
        if self._closed:
            return
            
        try:
            # 关闭异步引擎
            if hasattr(self, 'async_engine') and self.async_engine:
                await self.async_engine.dispose()
            
            # 关闭同步引擎
            if hasattr(self, 'sync_engine') and self.sync_engine:
                self.sync_engine.dispose()
            
            # 关闭线程池
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=True)
                
            self._closed = True
        except Exception as e:
            logger.warning(f"关闭MySQL API缓存资源时出错: {e}")
    
    def __del__(self):
        """析构函数，关闭线程池（避免异步资源清理错误）"""
        if self._closed:
            return
            
        try:
            # 只关闭线程池，避免在事件循环关闭后操作异步资源
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=False)
                
            # 同步引擎可以安全关闭
            if hasattr(self, 'sync_engine') and self.sync_engine:
                self.sync_engine.dispose()
        except Exception:
            pass

class AsyncMySQLCrawlCache:
    """
    基于URL和Topic联合主键的MySQL缓存
    专门用于爬取结果的缓存
    """
    
    def __init__(self, table_name: str, max_retries: int = 3, retry_delay: float = 0.5):
        """
        初始化异步MySQL爬取缓存
        
        Args:
            table_name: 表名
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
        """
        # 表名和重试配置
        self.table_name = table_name
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # 同步引擎，用于写入操作和表创建
        self.sync_engine = create_engine(
            get_mysql_url(async_driver=False),
            **ENGINE_CONFIG,
            connect_args={'charset': 'utf8mb4'}
        )
        
        # 异步引擎，用于读取操作
        self.async_engine = create_async_engine(
            get_mysql_url(async_driver=True),
            **ENGINE_CONFIG
        )
        
        # 创建会话
        self.sync_session = sessionmaker(bind=self.sync_engine)
        self.async_session = sessionmaker(
            self.async_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # 线程池用于执行同步写入操作
        self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="mysql_crawl_cache_writer")
        
        # 初始化表
        self._setup_table()
        
        # 线程锁，用于同步写入操作
        self._lock = threading.Lock()
        
        # 标记是否已关闭
        self._closed = False
        
    def _setup_table(self):
        """创建爬取缓存表结构"""
        try:
            # 创建爬取缓存表，使用url和topic作为联合主键
            with self.sync_engine.connect() as conn:
                create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    `url` VARCHAR(512) NOT NULL,
                    `topic` VARCHAR(255) NOT NULL,
                    `content` LONGTEXT,
                    `request_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`url`, `topic`),
                    INDEX `idx_url` (`url`),
                    INDEX `idx_topic` (`topic`),
                    INDEX `idx_request_time` (`request_time`),
                    INDEX `idx_updated_at` (`updated_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                conn.execute(text(create_table_sql))
                conn.commit()
                logger.info(f"MySQL爬取缓存表 {self.table_name} 初始化完成")
        except Exception as e:
            logger.error(f"创建MySQL爬取缓存表失败: {e}")
            
    def _sync_retry_operation(self, operation, *args, **kwargs):
        """
        同步重试数据库操作
        
        Args:
            operation: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                return operation(*args, **kwargs)
            except (OperationalError, SQLAlchemyError) as e:
                last_error = e
                error_str = str(e).lower()
                
                # MySQL常见的可重试错误
                retry_keywords = [
                    'connection', 'timeout', 'lost connection', 'gone away',
                    'deadlock', 'lock wait timeout', 'too many connections'
                ]
                
                if any(keyword in error_str for keyword in retry_keywords):
                    if attempt < self.max_retries:
                        logger.warning(f"MySQL爬取缓存操作失败（第 {attempt} 次尝试），准备重试: {e}")
                        time.sleep(self.retry_delay * attempt)  # 指数退避
                        continue
                    else:
                        logger.error(f"MySQL爬取缓存操作失败，所有 {self.max_retries} 次尝试均失败: {e}")
                        break
                else:
                    # 非重试类错误，直接抛出
                    logger.error(f"MySQL爬取缓存操作失败（非重试类错误）: {e}")
                    break
            except Exception as e:
                logger.error(f"MySQL爬取缓存操作发生未知错误: {e}")
                break
        
        return None

    async def _async_retry_operation(self, operation, *args, **kwargs):
        """
        异步重试数据库操作（用于读取）
        
        Args:
            operation: 要执行的操作函数
            *args, **kwargs: 操作函数的参数
            
        Returns:
            操作结果
        """
        last_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                return await operation(*args, **kwargs)
            except (OperationalError, SQLAlchemyError) as e:
                last_error = e
                error_str = str(e).lower()
                
                # MySQL常见的可重试错误
                retry_keywords = [
                    'connection', 'timeout', 'lost connection', 'gone away',
                    'deadlock', 'lock wait timeout', 'too many connections'
                ]
                
                if any(keyword in error_str for keyword in retry_keywords):
                    if attempt < self.max_retries:
                        logger.warning(f"异步MySQL爬取缓存操作失败（第 {attempt} 次尝试），准备重试: {e}")
                        await asyncio.sleep(self.retry_delay * attempt)  # 异步睡眠
                        continue
                    else:
                        logger.error(f"异步MySQL爬取缓存操作失败，所有 {self.max_retries} 次尝试均失败: {e}")
                        break
                else:
                    # 非重试类错误，直接抛出
                    logger.error(f"异步MySQL爬取缓存操作失败（非重试类错误）: {e}")
                    break
            except Exception as e:
                logger.error(f"异步MySQL爬取缓存操作发生未知错误: {e}")
                break
        
        return None
        
    async def get(self, url: str, topic: str) -> Optional[Dict[str, Any]]:
        """
        异步获取缓存值（基于url和topic）
        
        Args:
            url: URL
            topic: 主题
        
        Returns:
            缓存的结果字典，包含content和request_time，如果不存在则返回None
        """
        async def _get_operation():
            async with self.async_session() as session:
                # 使用参数化查询
                query = text(f"SELECT `content`, `request_time` FROM {self.table_name} WHERE `url` = :url AND `topic` = :topic")
                result = await session.execute(query, {"url": url, "topic": topic})
                row = result.fetchone()
                
                if row:
                    # 尝试解析JSON内容
                    try:
                        content = json.loads(row[0]) if row[0] else None
                    except json.JSONDecodeError:
                        content = row[0]
                    
                    return {
                        "content": content,
                        "request_time": row[1],
                        "cached": True
                    }
            return None
        
        return await self._async_retry_operation(_get_operation)
    
    def _sync_set(self, url: str, topic: str, content: Any) -> None:
        """
        同步设置缓存值
        """
        def _set_operation():
            with self._lock:
                with self.sync_session() as session:
                    # 将内容转换为JSON字符串
                    if isinstance(content, (dict, list)):
                        content_str = json.dumps(content, ensure_ascii=False)
                    else:
                        content_str = str(content) if content is not None else ""
                    
                    # 使用MySQL的ON DUPLICATE KEY UPDATE语法
                    stmt = text(f"""
                    INSERT INTO {self.table_name} (`url`, `topic`, `content`, `request_time`, `updated_at`) 
                    VALUES (:url, :topic, :content, :request_time, :updated_at)
                    ON DUPLICATE KEY UPDATE 
                    `content` = VALUES(`content`), 
                    `request_time` = VALUES(`request_time`),
                    `updated_at` = VALUES(`updated_at`)
                    """)
                    
                    now = datetime.now(timezone.utc)
                    params = {
                        "url": url,
                        "topic": topic,
                        "content": content_str,
                        "request_time": now,
                        "updated_at": now
                    }
                    
                    session.execute(stmt, params)
                    session.commit()
        
        return self._sync_retry_operation(_set_operation)
    
    async def set(self, url: str, topic: str, content: Any) -> None:
        """
        异步设置缓存值（基于url和topic）
        
        Args:
            url: URL
            topic: 主题
            content: 要缓存的内容
        """
        # 在线程池中执行同步写入操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_set, url, topic, content)
        except Exception as e:
            logger.warning(f"MySQL爬取缓存写入失败，但不影响程序运行: {e}")
    
    def _sync_delete(self, url: str, topic: str) -> None:
        """
        同步删除缓存
        """
        def _delete_operation():
            with self._lock:
                with self.sync_session() as session:
                    stmt = text(f"DELETE FROM {self.table_name} WHERE `url` = :url AND `topic` = :topic")
                    session.execute(stmt, {"url": url, "topic": topic})
                    session.commit()
        
        return self._sync_retry_operation(_delete_operation)
        
    async def delete(self, url: str, topic: str) -> None:
        """
        异步删除缓存（基于url和topic）
        
        Args:
            url: URL
            topic: 主题
        """
        # 在线程池中执行同步删除操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_delete, url, topic)
        except Exception as e:
            logger.warning(f"MySQL爬取缓存删除失败: {e}")
    
    def _sync_clear(self) -> None:
        """
        同步清空缓存表
        """
        def _clear_operation():
            with self._lock:
                with self.sync_session() as session:
                    stmt = text(f"DELETE FROM {self.table_name}")
                    session.execute(stmt)
                    session.commit()
        
        return self._sync_retry_operation(_clear_operation)
        
    async def clear(self) -> None:
        """异步清空缓存表（清空操作在线程池中同步执行）"""
        # 在线程池中执行同步清空操作
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._sync_clear)
        except Exception as e:
            logger.warning(f"MySQL爬取缓存清空失败: {e}")
    
    async def close(self):
        """正确关闭所有资源"""
        if self._closed:
            return
            
        try:
            # 关闭异步引擎
            if hasattr(self, 'async_engine') and self.async_engine:
                await self.async_engine.dispose()
            
            # 关闭同步引擎
            if hasattr(self, 'sync_engine') and self.sync_engine:
                self.sync_engine.dispose()
            
            # 关闭线程池
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=True)
                
            self._closed = True
        except Exception as e:
            logger.warning(f"关闭MySQL爬取缓存资源时出错: {e}")
    
    def __del__(self):
        """析构函数，关闭线程池（避免异步资源清理错误）"""
        if self._closed:
            return
            
        try:
            # 只关闭线程池，避免在事件循环关闭后操作异步资源
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=False)
                
            # 同步引擎可以安全关闭
            if hasattr(self, 'sync_engine') and self.sync_engine:
                self.sync_engine.dispose()
        except Exception:
            pass 