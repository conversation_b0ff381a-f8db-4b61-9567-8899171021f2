import asyncio
import logging
from typing import Optional, Dict, Any, List
import aiohttp
import json

# 导入缓存配置（使用单例模式）
from ..config.cache_config import get_cache_instance

logger = logging.getLogger(__name__)


class CrawlMarkdownClient:
    """用于调用爬取和markdown转换API的异步客户端"""
    
    def __init__(self, base_url: str = "http://localhost:9003", enable_cache: bool = True):
        """
        初始化爬取markdown客户端
        
        Args:
            base_url: API服务的基础URL
            enable_cache: 是否启用MySQL缓存
        """
        self.base_url = base_url.rstrip('/')
        self.crawl_endpoint = f"{self.base_url}/crawl_and_markdown/"
        self.enable_cache = enable_cache
        
        # 使用单例缓存实例
        if self.enable_cache:
            try:
                self.cache = get_cache_instance('crawl')
                # 只有当缓存实例是新创建的时候才输出初始化日志
                # 由于get_cache_instance现在使用单例模式，我们可以简化日志
                logger.debug("MySQL爬取缓存已连接（单例模式）")
            except Exception as e:
                logger.warning(f"无法初始化MySQL缓存，缓存功能将被禁用: {e}")
                self.cache = None
                self.enable_cache = False
        else:
            self.cache = None
        
    async def crawl_and_markdown(
        self, 
        topic: str, 
        url: str, 
        url_keywords: Optional[List[str]] = [],
        timeout: int = 800,
        use_cache: bool = True,
    ) -> Optional[Dict[str, Any]]:
        """
        调用爬取和markdown转换API
        
        Args:
            topic: 爬取主题
            url: 要爬取的URL
            url_keywords: URL关键词列表
            timeout: 请求超时时间（秒）
            use_cache: 是否使用缓存
            save_db: 是否保存到数据库（可以覆盖已有缓存）
            
        Returns:
            API响应结果，如果失败则返回None
        """
        # 1. 尝试从缓存获取（如果启用缓存且允许使用缓存）
        if self.enable_cache and self.cache and use_cache:
            try:
                cached_result = await self.cache.get(url, topic)
                if cached_result:
                    logger.info(f"从缓存获取爬取结果: {url}, 主题: {topic}")
                    return cached_result["content"]
            except Exception as e:
                logger.warning(f"从缓存获取数据失败: {e}")
        
        # 2. 执行实际的API调用
        payload = {
            "topic": topic,
            "url": url,
            "url_keywords": url_keywords
        }
        
        try:
            logger.info(f"开始爬取和转换markdown: {url}, 主题: {topic}")
            
            timeout_obj = aiohttp.ClientTimeout(total=timeout)
            
            async with aiohttp.ClientSession(timeout=timeout_obj) as session:
                async with session.post(
                    self.crawl_endpoint,
                    json=payload,
                    headers={
                        'accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"成功完成爬取和转换: {url}")
                        
                        # 3. 保存到缓存（如果启用缓存且允许保存）
                        if self.enable_cache and self.cache:
                            try:
                                await self.cache.set(url, topic, result)
                                logger.info(f"爬取结果已保存到缓存: {url}, 主题: {topic}")
                            except Exception as e:
                                logger.warning(f"保存到缓存失败: {e}")
                        
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"API请求失败 [{url}]: 状态码 {response.status}, 错误: {error_text}")
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}",
                            "status_code": response.status
                        }
                        
        except asyncio.TimeoutError:
            logger.error(f"请求超时 {url}: 超过 {timeout} 秒")
            return {
                "success": False,
                "error": f"请求超时: 超过 {timeout} 秒"
            }
        except aiohttp.ClientError as e:
            logger.error(f"网络请求错误 {url}: {str(e)}")
            return {
                "success": False,
                "error": f"网络请求错误: {str(e)}"
            }
        except Exception as e:
            logger.error(f"爬取和转换markdown时发生未知错误 {url}: {str(e)}")
            return {
                "success": False,
                "error": f"未知错误: {str(e)}"
            }
    
    async def batch_crawl_and_markdown(
        self, 
        requests: List[Dict[str, Any]], 
        concurrent_limit: int = 3
    ) -> Dict[str, Dict[str, Any]]:
        """
        批量爬取和转换markdown
        
        Args:
            requests: 请求列表，每个元素包含 topic, url, url_keywords, use_cache, save_db
            concurrent_limit: 并发限制
            
        Returns:
            结果字典，键为URL，值为对应的响应结果
        """
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def process_single_request(request_data: Dict[str, Any]) -> tuple:
            async with semaphore:
                url = request_data["url"]
                result = await self.crawl_and_markdown(
                    topic=request_data["topic"],
                    url=url,
                    url_keywords=request_data.get("url_keywords"),
                    timeout=request_data.get("timeout", 30),
                    use_cache=request_data.get("use_cache", True),
                    save_db=request_data.get("save_db", True)
                )
                return url, result
        
        # 创建所有任务
        tasks = [process_single_request(req) for req in requests]
        
        # 执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        final_results = {}
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"批量处理中发生异常: {str(result)}")
                continue
            
            url, response = result
            final_results[url] = response
        
        return final_results
    
    async def health_check(self) -> bool:
        """
        检查API服务是否可用
        
        Returns:
            True如果服务可用，False否则
        """
        try:
            timeout_obj = aiohttp.ClientTimeout(total=5)
            
            async with aiohttp.ClientSession(timeout=timeout_obj) as session:
                # 尝试访问根路径或健康检查端点
                health_url = f"{self.base_url}/health" if "/health" else f"{self.base_url}/"
                
                async with session.get(health_url) as response:
                    if response.status in [200, 404]:  # 404也表示服务在运行
                        logger.info(f"API服务可用: {self.base_url}")
                        return True
                    else:
                        logger.warning(f"API服务状态异常: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return False
    
    async def clear_cache(self) -> bool:
        """
        清空缓存
        
        Returns:
            True如果成功清空，False否则
        """
        if not self.enable_cache or not self.cache:
            logger.warning("缓存未启用，无法清空")
            return False
        
        try:
            await self.cache.clear()
            logger.info("缓存已清空")
            return True
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False
    
    async def delete_cache(self, url: str, topic: str) -> bool:
        """
        删除特定的缓存项
        
        Args:
            url: URL
            topic: 主题
            
        Returns:
            True如果成功删除，False否则
        """
        if not self.enable_cache or not self.cache:
            logger.warning("缓存未启用，无法删除")
            return False
        
        try:
            await self.cache.delete(url, topic)
            logger.info(f"已删除缓存: {url}, 主题: {topic}")
            return True
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
            return False


# 全局单例客户端实例缓存
_global_crawl_clients = {}

# 便捷函数
async def crawl_company_info(
    url: str, 
    topic: str = "公司介绍和产品信息",
    url_keywords: Optional[List[str]] = None,
    base_url: str = "http://localhost:9003",
    use_cache: bool = True,
) -> Optional[Dict[str, Any]]:
    """
    便捷函数：爬取公司信息并转换为markdown（使用单例客户端）
    
    Args:
        url: 公司网站URL
        topic: 爬取主题
        url_keywords: URL关键词列表
        base_url: API服务地址
        use_cache: 是否使用缓存
        
    Returns:
        爬取结果
    """
    try:
        # 使用单例模式，避免重复创建客户端实例
        if base_url not in _global_crawl_clients:
            _global_crawl_clients[base_url] = CrawlMarkdownClient(base_url)
        
        client = _global_crawl_clients[base_url]
        return await client.crawl_and_markdown(topic, url, url_keywords, use_cache=use_cache)
    except Exception as e:
        logger.error(f"爬取和转换markdown时发生未知错误 {url}: {str(e)}")
        return ""


# 示例用法
async def main():
    """示例用法"""
    client = CrawlMarkdownClient()
    
    print("=== 爬取Markdown客户端示例（带MySQL缓存） ===\n")
    
    # 1. 健康检查
    print("1. 检查API服务状态...")
    is_healthy = await client.health_check()
    if is_healthy:
        print("✅ API服务可用")
    else:
        print("❌ API服务不可用")
        return
    
    print("\n" + "-" * 50)
    
    # 2. 第一次爬取（从API获取并保存到缓存）
    print("2. 第一次爬取（从API获取并保存到缓存）...")
    result = await client.crawl_and_markdown(
        topic="公司介绍和产品信息",
        url="https://www.spacepioneer.cc",
        url_keywords=["about", "company", "brand"],
        use_cache=False,  # 不使用缓存
        save_db=True      # 保存到数据库
    )
    
    if result:
        if result.get("success", True):
            print("✅ 第一次爬取成功，数据已保存到缓存")
        else:
            print(f"❌ 第一次爬取失败: {result.get('error', '未知错误')}")
    
    print("\n" + "-" * 50)
    
    # 3. 第二次爬取（从缓存获取）
    print("3. 第二次爬取（从缓存获取）...")
    result = await client.crawl_and_markdown(
        topic="公司介绍和产品信息",
        url="https://www.spacepioneer.cc",
        url_keywords=["about", "company", "brand"],
        use_cache=True,   # 使用缓存
        save_db=False     # 不保存到数据库
    )
    
    if result:
        if result.get("success", True):
            # 检查是否来自缓存
            if result.get("cached"):
                print("✅ 从缓存获取数据成功")
            else:
                print("✅ 从API获取数据成功")
        else:
            print(f"❌ 第二次爬取失败: {result.get('error', '未知错误')}")
    
    print("\n" + "-" * 50)
    
    # 4. 强制刷新缓存（覆盖已有缓存）
    print("4. 强制刷新缓存（覆盖已有缓存）...")
    result = await client.crawl_and_markdown(
        topic="公司介绍和产品信息",
        url="https://www.spacepioneer.cc",
        url_keywords=["about", "company", "brand"],
        use_cache=False,  # 不使用缓存
        save_db=True      # 保存到数据库（覆盖）
    )
    
    if result:
        if result.get("success", True):
            print("✅ 缓存已刷新")
        else:
            print(f"❌ 缓存刷新失败: {result.get('error', '未知错误')}")


if __name__ == "__main__":
    asyncio.run(main()) 