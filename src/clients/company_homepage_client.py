import asyncio
import logging
import re
from urllib.parse import urljoin, urlparse
from typing import Optional, Dict, Any, List, Tuple
from crawl4ai import AsyncWeb<PERSON>raw<PERSON>, CrawlerRunConfig, CacheMode
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

logger = logging.getLogger(__name__)


class CompanyHomepageClient:
    """用于获取公司主页内容的客户端"""
    
    def __init__(self):
        """初始化公司主页客户端"""
        self.md_generator = DefaultMarkdownGenerator(
            options={
                "ignore_links": True,    # 忽略所有链接
                "ignore_images": True    # 忽略所有图片
            }
        )
        self.config = CrawlerRunConfig(
            word_count_threshold=10,
            excluded_tags=["nav", "footer"],
            exclude_external_links=True,
            exclude_social_media_links=True,
            exclude_domains=["ads.com", "spammytrackers.net"],
            exclude_external_images=True,
            cache_mode=CacheMode.BYPASS,
            markdown_generator=self.md_generator
        )
        
        # 用于检测"关于我们"页面的配置，保留链接以便提取URL
        self.config_with_links = CrawlerRunConfig(
            word_count_threshold=10,
            excluded_tags=["nav", "footer"],
            exclude_external_links=True,
            exclude_social_media_links=True,
            exclude_domains=["ads.com", "spammytrackers.net"],
            exclude_external_images=True,
            cache_mode=CacheMode.BYPASS,
            markdown_generator=DefaultMarkdownGenerator(
                options={
                    "ignore_links": False,   # 保留链接以便提取URL
                    "ignore_images": True    # 忽略图片
                }
            )
        )
        
        # "关于我们"页面的常见关键词
        self.about_us_keywords = [
            "关于我们", "关于", "公司简介", "企业简介", "公司介绍", "企业介绍",
            "about us", "about", "company", "about company", "our company",
            "who we are", "our story", "company profile", "corporate profile"
        ]
    
    async def get_homepage_content(self, url: str) -> Optional[str]:
        """
        获取指定URL的主页内容
        
        Args:
            url: 公司主页URL
            
        Returns:
            主页内容的markdown格式文本，如果获取失败则返回None
        """
        try:
            logger.info(f"开始获取主页内容: {url}")
            
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(
                    url=url,
                    config=self.config
                )
                
                if result and result.markdown:
                    logger.info(f"成功获取主页内容，长度: {len(result.markdown)} 字符")
                    return result.markdown
                else:
                    logger.warning(f"未能获取到有效的主页内容: {url}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取主页内容时发生错误 {url}: {str(e)}")
            return None
    
    async def get_multiple_homepages(self, urls: list[str]) -> Dict[str, Optional[str]]:
        """
        批量获取多个公司主页内容
        
        Args:
            urls: 公司主页URL列表
            
        Returns:
            字典，键为URL，值为对应的markdown内容
        """
        results = {}
        
        for url in urls:
            content = await self.get_homepage_content(url)
            results[url] = content
            
        return results
    
    async def get_homepage_with_metadata(self, url: str) -> Optional[Dict[str, Any]]:
        """
        获取主页内容及元数据
        
        Args:
            url: 公司主页URL
            
        Returns:
            包含内容和元数据的字典
        """
        try:
            logger.info(f"开始获取主页内容和元数据: {url}")
            
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(
                    url=url,
                    config=self.config
                )
                
                if result:
                    return {
                        "url": url,
                        "markdown": result.markdown,
                        "html": result.html,
                        "success": result.success,
                        "status_code": getattr(result, 'status_code', None),
                        "response_headers": getattr(result, 'response_headers', {}),
                        "extracted_content": getattr(result, 'extracted_content', None)
                    }
                else:
                    logger.warning(f"未能获取到结果: {url}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取主页内容和元数据时发生错误 {url}: {str(e)}")
            return None
    
    def _extract_about_us_links(self, html_content: str, base_url: str) -> List[str]:
        """
        从HTML内容中提取可能的"关于我们"页面链接
        
        Args:
            html_content: HTML内容
            base_url: 基础URL
            
        Returns:
            可能的"关于我们"页面URL列表
        """
        about_links = []
        
        # 使用正则表达式查找链接
        link_pattern = r'<a[^>]+href=["\']([^"\']+)["\'][^>]*>([^<]+)</a>'
        matches = re.findall(link_pattern, html_content, re.IGNORECASE)
        
        for href, text in matches:
            # 检查链接文本是否包含"关于我们"相关关键词
            text_lower = text.lower().strip()
            for keyword in self.about_us_keywords:
                if keyword.lower() in text_lower:
                    # 转换为绝对URL
                    absolute_url = urljoin(base_url, href)
                    if absolute_url not in about_links:
                        about_links.append(absolute_url)
                    break
        
        # 也检查href属性中是否包含关键词
        href_pattern = r'href=["\']([^"\']*(?:about|关于)[^"\']*)["\']'
        href_matches = re.findall(href_pattern, html_content, re.IGNORECASE)
        
        for href in href_matches:
            absolute_url = urljoin(base_url, href)
            if absolute_url not in about_links:
                about_links.append(absolute_url)
        
        return self._filter_main_about_pages(about_links)
    
    def _filter_main_about_pages(self, about_links: List[str]) -> List[str]:
        """
        过滤出主要的"关于我们"页面，避免抓取过多子页面
        
        Args:
            about_links: 所有找到的关于我们页面链接
            
        Returns:
            过滤后的主要页面链接列表
        """
        if not about_links:
            return []
        
        # 去重：移除只有尾部斜杠差异的重复URL
        def normalize_url(url: str) -> str:
            """标准化URL，移除尾部斜杠"""
            return url.rstrip('/')
        
        # 使用字典去重，保留第一个遇到的URL
        unique_links = {}
        for link in about_links:
            normalized = normalize_url(link)
            if normalized not in unique_links:
                unique_links[normalized] = link
        
        deduplicated_links = list(unique_links.values())
        
        # 按URL长度和优先级排序，优先选择更简洁的主页面
        def get_priority(url: str) -> Tuple[int, int]:
            """
            计算URL的优先级
            返回 (优先级分数, URL长度)，分数越低优先级越高
            """
            url_lower = url.lower()
            normalized = normalize_url(url_lower)
            
            # 最高优先级：直接的about页面
            if normalized.endswith('/about'):
                return (1, len(url))
            
            # 次高优先级：包含about但路径简单的页面
            if '/about/' in url_lower:
                # 计算路径深度
                path_depth = url_lower.count('/')
                return (2, path_depth)
            
            # 较低优先级：其他包含关键词的页面
            return (3, len(url))
        
        # 排序并选择最优的页面
        sorted_links = sorted(deduplicated_links, key=get_priority)
        
        # 只选择1个最主要的页面
        main_links = []
        for link in sorted_links:
            # 避免选择明显的子页面或特定功能页面
            link_lower = link.lower()
            
            # 跳过明显的子页面
            skip_patterns = [
                '/store/', '/brand/', '/team/', '/history/', 
                '/contact/', '/news/', '/careers/', '/jobs/',
                '/location/', '/office/', '/branch/'
            ]
            
            if any(pattern in link_lower for pattern in skip_patterns):
                continue
                
            main_links.append(link)
            
            # 只选择1个主要页面
            break
        
        # 如果没有找到合适的主页面，返回第一个
        if not main_links and sorted_links:
            main_links = [sorted_links[0]]
        
        return main_links
    
    async def find_about_us_links(self, url: str) -> List[str]:
        """
        查找主页中的"关于我们"页面链接
        
        Args:
            url: 公司主页URL
            
        Returns:
            "关于我们"页面URL列表
        """
        try:
            logger.info(f"开始查找关于我们页面链接: {url}")
            
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(
                    url=url,
                    config=self.config_with_links
                )
                
                if result and result.html:
                    about_links = self._extract_about_us_links(result.html, url)
                    logger.info(f"找到 {len(about_links)} 个可能的关于我们页面链接")
                    return about_links
                else:
                    logger.warning(f"未能获取到HTML内容: {url}")
                    return []
                    
        except Exception as e:
            logger.error(f"查找关于我们页面链接时发生错误 {url}: {str(e)}")
            return []
    
    async def get_about_us_content(self, about_url: str) -> Optional[str]:
        """
        获取"关于我们"页面内容
        
        Args:
            about_url: "关于我们"页面URL
            
        Returns:
            页面内容的markdown格式文本
        """
        try:
            logger.info(f"开始获取关于我们页面内容: {about_url}")
            
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(
                    url=about_url,
                    config=self.config
                )
                
                if result and result.markdown:
                    logger.info(f"成功获取关于我们页面内容，长度: {len(result.markdown)} 字符")
                    return result.markdown
                else:
                    logger.warning(f"未能获取到有效的关于我们页面内容: {about_url}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取关于我们页面内容时发生错误 {about_url}: {str(e)}")
            return None
    
    async def get_homepage_with_about_us(self, url: str) -> Dict[str, Any]:
        """
        获取主页内容以及"关于我们"页面内容
        
        Args:
            url: 公司主页URL
            
        Returns:
            包含主页和关于我们页面内容的字典
        """
        result = {
            "homepage_url": url,
            "homepage_content": None,
            "about_us_links": [],
            "about_us_contents": {},
            "success": False
        }
        
        try:
            # 获取主页内容
            logger.info(f"开始获取完整的公司信息: {url}")
            homepage_content = await self.get_homepage_content(url)
            result["homepage_content"] = homepage_content
            
            # 查找"关于我们"页面链接
            about_links = await self.find_about_us_links(url)
            result["about_us_links"] = about_links
            
            # 获取所有"关于我们"页面内容
            for about_url in about_links:
                about_content = await self.get_about_us_content(about_url)
                if about_content:
                    result["about_us_contents"][about_url] = about_content
            
            result["success"] = True
            logger.info(f"成功获取完整公司信息，主页内容: {'有' if homepage_content else '无'}，关于我们页面: {len(result['about_us_contents'])} 个")
            
        except Exception as e:
            logger.error(f"获取完整公司信息时发生错误 {url}: {str(e)}")
            result["error"] = str(e)
        
        return result


# 便捷函数
async def get_company_homepage(url: str) -> Optional[str]:
    """
    便捷函数：获取单个公司主页内容
    
    Args:
        url: 公司主页URL
        
    Returns:
        主页内容的markdown格式文本
    """
    client = CompanyHomepageClient()
    return await client.get_homepage_content(url)


async def get_company_full_info(url: str) -> Dict[str, Any]:
    """
    便捷函数：获取公司完整信息（主页 + 关于我们页面）
    
    Args:
        url: 公司主页URL
        
    Returns:
        包含主页和关于我们页面内容的字典
    """
    client = CompanyHomepageClient()
    return await client.get_homepage_with_about_us(url)


# 示例用法
async def main():
    """示例用法"""
    client = CompanyHomepageClient()
    test_url = "https://www.pupuwhale.com/"
    
    print("=== 公司主页客户端示例 ===\n")
    
    # 1. 获取单个主页内容
    print("1. 获取主页内容...")
    content = await client.get_homepage_content(test_url)
    if content:
        print(f"✅ 主页内容获取成功，长度: {len(content)} 字符")
        print(f"内容预览: {content[:200]}...\n")
    else:
        print("❌ 主页内容获取失败\n")
    
    # 2. 查找"关于我们"页面链接
    print("2. 查找关于我们页面链接...")
    about_links = await client.find_about_us_links(test_url)
    if about_links:
        print(f"✅ 找到 {len(about_links)} 个关于我们页面链接:")
        for i, link in enumerate(about_links, 1):
            print(f"   {i}. {link}")
        print()
    else:
        print("❌ 未找到关于我们页面链接\n")
    
    # 3. 获取完整公司信息（主页 + 关于我们）
    print("3. 获取完整公司信息...")
    full_info = await client.get_homepage_with_about_us(test_url)
    if full_info["success"]:
        print(f"✅ 完整信息获取成功:")
        print(f"   主页内容: {'有' if full_info['homepage_content'] else '无'}")
        print(f"   关于我们链接数量: {len(full_info['about_us_links'])}")
        print(f"   关于我们页面内容数量: {len(full_info['about_us_contents'])}")
        
        # 显示关于我们页面内容预览
        for url, content in full_info['about_us_contents'].items():
            print(f"\n   关于我们页面 ({url}):")
            print(f"   内容长度: {len(content)} 字符")
            print(f"   内容预览: {content[:150]}...")
    else:
        print("❌ 完整信息获取失败")
        if "error" in full_info:
            print(f"   错误: {full_info['error']}")


if __name__ == "__main__":
    asyncio.run(main()) 