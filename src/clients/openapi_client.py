import asyncio
import logging
import json
import time
from typing import List, Optional

import aiohttp

from pydantic import BaseModel
from ..config.cache_config import get_cache_instance

logger = logging.getLogger(__name__)

class APICall(BaseModel):
    api_id: str
    name: str
    request_params: dict
    full_response: bool = False
    response_format: str = "json"


class TycOpenApiToolClient:

    def __init__(self, host_url: Optional[str] = None, max_retries: int = 3, retry_delay: float = 1.0):
        if host_url is None:
            self.host_url = "http://openapi-tool-pre.jindidata.com/api/v1/tools"
        else:
            self.host_url = host_url
        self.session = None
        self._in_context_manager = False
        self.max_retries = max_retries  # 最大重试次数
        self.retry_delay = retry_delay  # 重试延迟时间（秒）
        # 延迟初始化缓存实例，只在需要时创建
        self._cache = None

    @property
    def cache(self):
        """延迟初始化缓存实例"""
        if self._cache is None:
            self._cache = get_cache_instance('tyc_api')
        return self._cache

    async def __aenter__(self):
        if self.session is None:
            self.session = aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(
                    ssl=False,
                    limit=100,  # Increase connection limit for high concurrency
                    enable_cleanup_closed=True
                ),
                timeout=aiohttp.ClientTimeout(total=30)
            )
        self._in_context_manager = True
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
        self._in_context_manager = False

    async def _ensure_session(self):
        """Ensure we have an active session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(
                    ssl=False,
                    limit=100,  # Increase connection limit for high concurrency
                    enable_cleanup_closed=True
                ),
                timeout=aiohttp.ClientTimeout(total=30)
            )
        return self.session

    @staticmethod
    def format_tyc_openapi_results(results: List[dict]) -> str:
        formatted_results = ""
        for result in results:
            formatted_results += f"公司名称: {result['name']} | 公司id: {result['id']} | 匹配原因: {result['matchType']}\n\n"

        return formatted_results

    async def _search_entities(self, keyword: str, use_cache: bool = True) -> dict:
        # 检查缓存
        if use_cache:
            # 使用"search_entities"作为api_id，keyword作为company_id
            cached_result = await self.cache.get("search_entities", keyword)
            if cached_result is not None:
                logger.info(f"Cache hit for search_entities: keyword={keyword}")
                return cached_result

        tool_url = f"{self.host_url}/search-entities/"
        payload = {
            "entity_search_keyword": keyword
        }
        session = await self._ensure_session()
        
        # 添加重试逻辑
        for attempt in range(1, self.max_retries + 1):
            response_obj = None
            try:
                async with session.post(tool_url, json=payload) as response:
                    response_obj = response
                    result = await response.json()
                    
                    # 缓存结果
                    if use_cache:
                        await self.cache.set("search_entities", keyword, result)
                    
                    return result
            except aiohttp.ClientError as e:
                error_message = f"_search_entities 调用出错 (keyword: '{keyword}')"
                if response_obj is not None and hasattr(response_obj, 'headers'):
                    trace_id = response_obj.headers.get("X-Trace-Id")
                    if trace_id:
                        error_message += f"。X-Trace-Id: {trace_id}"
                
                # 如果还有重试机会，则记录警告并重试
                if attempt < self.max_retries:
                    logger.warning(f"{error_message}。第 {attempt} 次尝试失败，准备重试。错误: {e}")
                    await asyncio.sleep(self.retry_delay * attempt)  # 指数退避
                else:
                    # 最后一次尝试失败，记录错误
                    logger.error(f"{error_message}。所有 {self.max_retries} 次尝试均失败。错误: {e}")
                    return {}
        
        # 不应该到达这里，但为了安全起见
        return {}

    async def execute_api(self, api_call: APICall, use_cache: bool = True) -> dict:
        # 检查缓存
        if use_cache:
            # 从request_params中获取company_id（keyword字段）
            company_id = api_call.request_params.get("keyword", "")
            if company_id:
                cached_result = await self.cache.get(api_call.api_id, company_id)
                if cached_result is not None:
                    logger.info(f"Cache hit for execute_api: api_id={api_call.api_id}, company_id={company_id}")
                    return cached_result

        tool_url = f"{self.host_url}/execute-api/"
        payload = {
            "api_id": api_call.api_id,
            "request_params": api_call.request_params,
            "full_response": api_call.full_response,
            "response_format": api_call.response_format
        }
        session = await self._ensure_session()
        
        # 添加重试逻辑
        for attempt in range(1, self.max_retries + 1):
            response_obj = None
            try:
                async with session.post(tool_url, json=payload) as response:
                    response_obj = response
                    result = await response.json()
                    
                    # 只要调用了，就缓存
                    company_id = api_call.request_params.get("keyword", "")
                    if company_id:
                        await self.cache.set(api_call.api_id, company_id, result)
                    
                    return result
            except aiohttp.ClientError as e:
                error_message = f"execute_api 调用出错 (api_id: '{api_call.api_id}')"
                if response_obj is not None and hasattr(response_obj, 'headers'):
                    trace_id = response_obj.headers.get("X-Trace-Id")
                    if trace_id:
                        error_message += f"。X-Trace-Id: {trace_id}"
                
                # 如果还有重试机会，则记录警告并重试
                if attempt < self.max_retries:
                    logger.warning(f"{error_message}。第 {attempt} 次尝试失败，准备重试。错误: {e}")
                    await asyncio.sleep(self.retry_delay * attempt)  # 指数退避
                else:
                    # 最后一次尝试失败，记录错误
                    logger.error(f"{error_message}。所有 {self.max_retries} 次尝试均失败。错误: {e}")
                    return {}
        
        # 不应该到达这里，但为了安全起见
        return {}

    async def execute_apis(self, api_calls: List[APICall], use_cache: bool = True) -> dict:
        # 对于多个API调用，我们分别缓存每个API的结果
        async def _execute_single_api(api_call):
            try:
                return await self.execute_api(api_call, use_cache=use_cache)
            except Exception as e:
                logger.error(f"Error executing API {api_call.api_id}: {str(e)}")
                return {"error": str(e)}
        
        tasks = [_execute_single_api(api_call) for api_call in api_calls]
        results = await asyncio.gather(*tasks)
        
        return {api_call.name or api_call.api_id: result for api_call, result in zip(api_calls, results)}

    async def close(self):
        """Close the client session if it exists."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None


if __name__ == "__main__":
    async def main():
        async with TycOpenApiToolClient() as client:
            api_call = APICall(api_id="818", request_params={"keyword": "22822"})
            result = await client.execute_api(api_call, use_cache=True)
            print(result)
    
    asyncio.run(main())
