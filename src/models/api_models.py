from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel


class OneLinerResponse(BaseModel):
    """一句话总结响应模型"""
    company_id: str
    format_1: Optional[str] = None
    format_2: Optional[str] = None
    format_3: Optional[str] = None
    timeline: Optional[str] = None
    created_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CompanyResponse(BaseModel):
    """公司信息响应模型"""
    id: str
    timeline: Optional[str] = None
    one_liners: Optional[OneLinerResponse] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class CompanyListItem(BaseModel):
    """公司列表项模型"""
    id: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    has_one_liner: bool = False
    
    class Config:
        from_attributes = True


class CompanyListResponse(BaseModel):
    """公司列表响应模型"""
    total: int
    companies: List[CompanyListItem]


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str
    detail: Optional[str] = None


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    database: str
    timestamp: datetime


class Keyword(BaseModel):
    """关键词模型"""
    word: str
    start_index: int
    end_index: int
    source: str
    detail: str


class OneLinerWithKeywords(BaseModel):
    """包含关键词的一句话总结模型"""
    one_liner: str
    keywords: List[Keyword]


class Company0529Response(BaseModel):
    """公司信息响应模型（0529版本，支持关键词格式的一句话总结）"""
    id: str
    timeline: Optional[str] = None
    one_liners: Optional[OneLinerWithKeywords] = None  # 包含关键词的一句话总结
    processing_method: Optional[str] = None  # 处理方法
    limit_value: Optional[int] = None  # 使用的limit值
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True 