"""
缓存数据库模型
根据重构要求定义的四个缓存表模型
"""
from datetime import datetime, timezone
from sqlalchemy import Column, String, Text, DateTime, Integer, JSON
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class ApiResultsCache(Base):
    """API结果缓存表"""
    __tablename__ = 'api_results_cache'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_id = Column(String(50), index=True, nullable=False)  # 公司ID，建立索引
    api_name = Column(String(100), nullable=False)  # API名称
    api_id = Column(Integer, nullable=False)  # API ID
    api_result = Column(Text, nullable=False)  # API成功返回的完整结果（JSON或TEXT）
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc), 
                        onupdate=lambda: datetime.now(timezone.utc))  # 缓存更新时间
    
    def __repr__(self):
        return f"<ApiResultsCache(id={self.id}, company_id='{self.company_id}', api_name='{self.api_name}')>"


class HomepageResultsCache(Base):
    """主页结果缓存表"""
    __tablename__ = 'homepage_results_cache'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_id = Column(String(50), index=True, nullable=False)  # 公司ID，建立索引
    company_url = Column(String(500), nullable=False)  # 公司URL
    home_page_result = Column(Text, nullable=False)  # 主页抓取成功后的内容
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                        onupdate=lambda: datetime.now(timezone.utc))  # 缓存更新时间
    
    def __repr__(self):
        return f"<HomepageResultsCache(id={self.id}, company_id='{self.company_id}', url='{self.company_url}')>"


class LlmResultsCache(Base):
    """LLM结果缓存表"""
    __tablename__ = 'llm_results_cache'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_id = Column(String(50), index=True, nullable=False)  # 公司ID，建立索引
    system_prompt = Column(Text, nullable=False)  # 此次LLM调用的系统提示词
    content = Column(Text, nullable=False)  # 发送给LLM的用户内容
    llm_result = Column(Text, nullable=False)  # LLM成功返回的完整结果（JSON或TEXT）
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                        onupdate=lambda: datetime.now(timezone.utc))  # 缓存更新时间
    
    def __repr__(self):
        return f"<LlmResultsCache(id={self.id}, company_id='{self.company_id}')>"


class FinalResults(Base):
    """最终结果表"""
    __tablename__ = 'final_results'
    
    company_id = Column(String(50), primary_key=True)  # 公司ID，主键
    final_result = Column(JSON, nullable=False)  # 最终生成的结构化结果（JSON格式）
    update_time = Column(DateTime, default=lambda: datetime.now(timezone.utc),
                        onupdate=lambda: datetime.now(timezone.utc))  # 缓存更新时间
    
    def __repr__(self):
        return f"<FinalResults(company_id='{self.company_id}')>"
