from datetime import datetime, timezone
from sqlalchemy import Column, String, Text, DateTime, Integer, ForeignKey, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import relationship

# 导入MySQL配置
from ..config.mysql_config import get_mysql_url, ENGINE_CONFIG

Base = declarative_base()

class Company(Base):
    __tablename__ = 'companies'
    
    id = Column(String(50), primary_key=True)  # 公司ID
    timeline = Column(Text)  # 公司时间线内容
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    one_liners = relationship("OneLiner", back_populates="company", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Company(id='{self.id}', created_at='{self.created_at}')>"


class OneLiner(Base):
    __tablename__ = 'one_liners'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_id = Column(String(50), ForeignKey('companies.id'))
    timeline = Column(Text)  # 时间线内容副本
    format_1 = Column(Text)  # 第一种格式的一句话总结
    format_2 = Column(Text)  # 第二种格式的一句话总结
    format_3 = Column(Text)  # 第三种格式的一句话总结
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    company = relationship("Company", back_populates="one_liners")
    
    def __repr__(self):
        return f"<OneLiner(id={self.id}, company_id='{self.company_id}')>"


class OneLinerCache(Base):
    __tablename__ = 'one_liner_cache'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_id = Column(String(50), unique=True, index=True)  # 公司ID，唯一索引
    formatted_details = Column(Text)  # 格式化的公司详情
    one_liner = Column(Text)  # JSON格式的一句话总结
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    def __repr__(self):
        return f"<OneLinerCache(id={self.id}, company_id='{self.company_id}')>"


class OneLiner0529(Base):
    """0529版本的一句话总结缓存表"""
    __tablename__ = 'one_liner_0529'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_id = Column(String(50), unique=True, index=True)  # 公司ID，唯一索引
    formatted_details = Column(Text)  # 格式化的公司详情（输入内容）
    one_liner_json = Column(Text)  # JSON格式的一句话总结结果
    processing_method = Column(String(50), default='direct')  # 处理方法：direct 或 timeline
    limit_value = Column(Integer, default=20)  # 使用的limit值
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    def __repr__(self):
        return f"<OneLiner0529(id={self.id}, company_id='{self.company_id}', method='{self.processing_method}')>"


def init_db():
    """初始化数据库，创建表

    Returns:
        async_engine: 异步数据库引擎
    """
    # 使用MySQL
    mysql_url = get_mysql_url(async_driver=False)
    mysql_async_url = get_mysql_url(async_driver=True)

    # 创建同步引擎用于创建表
    engine = create_engine(mysql_url, **ENGINE_CONFIG)

    # 创建原有表
    Base.metadata.create_all(engine)

    # 创建新的缓存表
    from .cache_models import Base as CacheBase
    CacheBase.metadata.create_all(engine)

    # 创建异步引擎
    async_engine = create_async_engine(mysql_async_url, **ENGINE_CONFIG)

    return async_engine