#!/usr/bin/env python3

import asyncio
from direct_entity_processor import DirectEntityProcessor

async def test():
    processor = DirectEntityProcessor()
    result = await processor.process_single_entity('华帝科技')
    print('成功:', result.get('success', False))
    print('一句话总结:', result.get('one_liner', 'N/A'))
    print('从缓存:', result.get('from_cache', False))

if __name__ == "__main__":
    asyncio.run(test()) 