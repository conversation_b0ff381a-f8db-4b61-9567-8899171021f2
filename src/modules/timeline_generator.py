import asyncio
import json
import logging
import hashlib
from datetime import datetime
from typing import List, Dict
from .prompts import COMPANY_TIMELINE_PROMPT
from ..clients.llm_client import LLMClient

logger = logging.getLogger(__name__)

class TimelineGenerator:
    def __init__(self):
        # 移除TimelineGenerator层的缓存，因为上层CompanyTimeline已经有缓存
        self.llm_client = LLMClient()

    def format_documents(self, documents: Dict[str, str]) -> str:
        return "\n".join([f"<{api_name}>\n{doc}\n</{api_name}>" for api_name, doc in documents.items()])

    async def generate_timeline(self, documents: Dict[str, str], use_cache: bool = True) -> str:
        # 直接生成时间线，不在这一层做缓存
        prompt = COMPANY_TIMELINE_PROMPT.format(documents=self.format_documents(documents))
        
        response = await self.llm_client.generate_response(prompt, use_cache=use_cache)
        return response
