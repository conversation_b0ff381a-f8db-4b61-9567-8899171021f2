import asyncio
import json
import logging
from typing import List, Dict, Any, Union
from src.clients.llm_client import LLMClient
from prompts.one_liner_prompts import ONE_LINER_SYSTEM_PROMPT

logger = logging.getLogger(__name__)


class OneLiner:
    def __init__(self):
        self.llm_client = LLMClient()
    
    def _parse_one_liners(self, response: str) -> List[str]:
        """旧版解析方法，用于兼容旧格式"""
        one_liners = []
        if "<format_1>" in response:
            f1 = response.split("<format_1>")[1]
            f1 = f1.split("</format_1>")[0]
            one_liners.append(f1)
        if "<format_2>" in response:    
            f2 = response.split("<format_2>")[1]
            f2 = f2.split("</format_2>")[0]
            one_liners.append(f2)
        if "<format_3>" in response:
            f3 = response.split("<format_3>")[1]
            f3 = f3.split("</format_3>")[0]
            one_liners.append(f3)
        return one_liners

    def _clean_json_response(self, response: str) -> str:
        """清理LLM响应，提取JSON部分"""
        response = response.strip()
        
        # 查找JSON开始和结束位置
        start_idx = response.find('{')
        end_idx = response.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            return response[start_idx:end_idx + 1]
        
        return response

    async def generate_one_liners(self, passage: str, use_cache: bool = True) -> str:
        """
        生成一句话总结
        
        Args:
            passage: 输入的公司描述文本
            use_cache: 是否使用缓存
            
        Returns:
            JSON格式的字符串，包含title和keywords字段
        """
        user_prompt = passage
        message = [
            {"role": "system", "content": ONE_LINER_SYSTEM_PROMPT},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            # 获取LLM响应
            response = await self.llm_client.generate_response_with_messages(message, use_cache=use_cache)
            
            # 清理响应，提取JSON部分
            cleaned_response = self._clean_json_response(response)
            
            # 尝试解析JSON以验证格式
            try:
                parsed_json = json.loads(cleaned_response)
                # 验证必需字段
                if not isinstance(parsed_json, dict):
                    raise ValueError("响应不是字典格式")
                if "one_liner" not in parsed_json:
                    raise ValueError("响应缺少one_liner字段")
                if "keywords" not in parsed_json:
                    raise ValueError("响应缺少keywords字段")
                
                # 返回清理后的JSON字符串
                return cleaned_response
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败，原始响应: {response[:500]}...")
                logger.error(f"清理后响应: {cleaned_response[:500]}...")
                logger.error(f"JSON错误: {e}")
                
                # 尝试使用旧版解析方法作为fallback
                logger.warning("尝试使用旧版格式解析...")
                old_format_result = self._parse_one_liners(response)
                if old_format_result:
                    # 转换为新格式
                    fallback_result = {
                        "one_liner": old_format_result[0] if old_format_result else "公司信息解析失败",
                        "keywords": [
                            {
                                "word": "解析失败",
                                "source": "系统错误",
                                "detail": "LLM响应格式不符合预期"
                            }
                        ]
                    }
                    return json.dumps(fallback_result, ensure_ascii=False)
                
                # 如果所有方法都失败，返回错误格式
                error_result = {
                    "one_liner": "公司信息解析失败",
                    "keywords": [
                        {
                            "word": "JSON解析错误",
                            "source": "系统错误", 
                            "detail": f"无法解析LLM响应: {str(e)}"
                        }
                    ]
                }
                return json.dumps(error_result, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"生成一句话总结时发生错误: {e}")
            error_result = {
                "title": "公司信息获取失败",
                "keywords": [
                    {
                        "word": "系统错误",
                        "source": "系统错误",
                        "detail": f"处理过程中发生错误: {str(e)}"
                    }
                ]
            }
            return json.dumps(error_result, ensure_ascii=False)

    async def close(self):
        """关闭LLM客户端资源"""
        if hasattr(self.llm_client, 'close'):
            await self.llm_client.close()


if __name__ == "__main__":
    one_liner = OneLiner()
    # with open("company_details.txt", "r") as f:
    with open("company_timeline.txt", "r") as f:
        passage = f.read()
    one_liners = asyncio.run(one_liner.generate_one_liners(passage))
    with open("one_liners.txt", "w") as f:
        f.write(one_liners)
