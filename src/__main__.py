import sys
import os

# 将项目根目录添加到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

if __name__ == "__main__":
    if len(sys.argv) > 1:
        cmd = sys.argv[1]
        if cmd == "test_cache":
            from src.test_cache import test_cache
            import asyncio
            asyncio.run(test_cache())
        elif cmd == "test_new_cache":
            from src.test_new_cache import test_new_api_cache, test_company_timeline_cache
            import asyncio
            async def run_all_tests():
                await test_new_api_cache()
                await test_company_timeline_cache()
            asyncio.run(run_all_tests())
        elif cmd == "company_timeline":
            from src.company_timeline import CompanyTimeline
            import asyncio
            
            company_timeline = CompanyTimeline()
            company_id = sys.argv[2] if len(sys.argv) > 2 else "2349175912"
            use_cache = True  # 默认使用缓存，可以通过参数控制
            if len(sys.argv) > 3 and sys.argv[3] == "--no-cache":
                use_cache = False
                
            company_details = asyncio.run(company_timeline.get_company_details(company_id, use_cache=use_cache))
            
            with open("company_details.txt", "w") as f:
                for api_name, info in company_details.items():
                    f.write(f"{api_name}\n")
                    f.write(f"{info}\n")
                    f.write("-" * 100+"\n")
            print("company_details.txt 已保存")
            
            timeline = asyncio.run(company_timeline.get_company_timeline(company_details, use_cache=use_cache))
            with open("company_timeline.txt", "w") as f:
                f.write(timeline)
            print("company_timeline.txt 已保存")
        else:
            print(f"未知命令: {cmd}")
            print("可用命令: test_cache, test_new_cache, company_timeline [company_id] [--no-cache]")
    else:
        print("请指定命令: test_cache, test_new_cache, company_timeline [company_id] [--no-cache]") 