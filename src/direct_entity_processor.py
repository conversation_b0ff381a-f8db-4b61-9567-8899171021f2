"""
直接实体处理器
跳过timeline生成步骤，直接从company_details生成一句话总结并缓存到新的0529表
"""
import asyncio
import logging
import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from .company_timeline import CompanyTimeline
from .one_liner import OneLiner
from .models.db_models import init_db, Company, OneLiner as OneLinerModel, OneLinerCache, OneLiner0529
from .utils.open_api_compression import (
    process_patent_api_response,
    process_product_api_response,
    process_news_api_response,
    process_controller_api_response,
    process_investment_api_response,
    process_development_api_response,
    process_company_api_response
)
from .clients.crawl_markdown_client import crawl_company_info

logger = logging.getLogger(__name__)


class DirectEntityProcessor:
    """
    直接实体处理器
    跳过timeline生成，直接从公司详情生成一句话总结并缓存到OneLiner0529表
    """
    
    def __init__(self):
        self.company_timeline = CompanyTimeline()
        self.one_liner_generator = OneLiner()
        # 初始化数据库
        self.engine = init_db()
        self.Session = sessionmaker(bind=self.engine)
    
    async def close(self):
        """关闭所有资源"""
        try:
            # 关闭CompanyTimeline的资源
            if hasattr(self.company_timeline, 'close'):
                await self.company_timeline.close()
            
            # 关闭OneLiner的资源
            if hasattr(self.one_liner_generator, 'close'):
                await self.one_liner_generator.close()
            
            # 关闭数据库连接
            if hasattr(self.engine, 'dispose'):
                await self.engine.dispose()
                
        except Exception as e:
            logger.warning(f"关闭DirectEntityProcessor资源时出错: {e}")
    
    async def _get_from_cache(self, company_id: str) -> Optional[Dict[str, Any]]:
        """
        从OneLiner0529表获取公司的一句话总结缓存结果
        
        Args:
            company_id: 公司ID
            
        Returns:
            缓存的结果字典，如果不存在则返回None
        """
        session = None
        try:
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            result = await session.execute(
                select(OneLiner0529).filter_by(company_id=company_id)
            )
            cache_entry = result.scalars().first()
            
            if cache_entry:
                # 解析JSON格式的一句话总结
                one_liners = {}
                if cache_entry.one_liner_json:
                    try:
                        one_liners = json.loads(cache_entry.one_liner_json)
                    except json.JSONDecodeError:
                        logger.warning(f"公司 {company_id} 的缓存中一句话总结JSON解析失败")
                        one_liners = {}
                
                return {
                    "company_id": company_id,
                    "formatted_details": cache_entry.formatted_details,
                    "one_liners": one_liners,
                    "processing_method": cache_entry.processing_method,
                    "limit": cache_entry.limit_value,
                    "created_at": cache_entry.created_at,
                    "updated_at": cache_entry.updated_at,
                    "from_cache": True
                }
            
            return None
            
        except Exception as e:
            logger.error(f"从OneLiner0529缓存获取公司 {company_id} 结果时出错: {e}")
            return None
        finally:
            if session:
                await session.close()
    
    async def _save_to_cache(self, company_id: str, formatted_details: str, one_liners: Dict[str, Any], processing_method: str = "direct", limit_value: int = 20):
        """
        保存结果到OneLiner0529表
        
        Args:
            company_id: 公司ID
            formatted_details: 格式化的公司详情
            one_liners: 一句话总结字典
            processing_method: 处理方法，默认'direct'
            limit_value: 使用的limit值，默认20
        """
        session = None
        try:
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 检查是否已存在
            result = await session.execute(
                select(OneLiner0529).filter_by(company_id=company_id)
            )
            existing_cache = result.scalars().first()
            
            # 将一句话总结转换为JSON字符串
            one_liner_json = json.dumps(one_liners, ensure_ascii=False) if one_liners else "{}"
            
            if existing_cache:
                # 更新现有记录
                existing_cache.formatted_details = formatted_details
                existing_cache.one_liner_json = one_liner_json
                existing_cache.processing_method = processing_method
                existing_cache.limit_value = limit_value
                existing_cache.updated_at = datetime.now(timezone.utc)
            else:
                # 创建新记录
                cache_entry = OneLiner0529(
                    company_id=company_id,
                    formatted_details=formatted_details,
                    one_liner_json=one_liner_json,
                    processing_method=processing_method,
                    limit_value=limit_value
                )
                session.add(cache_entry)
            
            await session.commit()
            
        except Exception as e:
            if session:
                await session.rollback()
            logger.error(f"保存公司 {company_id} 到OneLiner0529缓存时出错: {e}")
            raise
        finally:
            if session:
                await session.close()
    
    async def clear_cache(self):
        """清空OneLiner0529缓存表"""
        session = None
        try:
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 删除所有缓存记录
            await session.execute(delete(OneLiner0529))
            await session.commit()
            
        except Exception as e:
            if session:
                await session.rollback()
            logger.error(f"清空OneLiner0529缓存表时出错: {e}")
            raise
        finally:
            if session:
                await session.close()
    
    async def list_cache_entries(self) -> List[Dict[str, Any]]:
        """列出OneLiner0529表中的所有缓存条目"""
        session = None
        try:
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            result = await session.execute(select(OneLiner0529))
            cache_entries = result.scalars().all()
            
            entries = []
            for entry in cache_entries:
                # 尝试解析一句话总结数量
                one_liner_count = 0
                if entry.one_liner_json:
                    try:
                        one_liners = json.loads(entry.one_liner_json)
                        one_liner_count = len(one_liners) if isinstance(one_liners, dict) else 0
                    except json.JSONDecodeError:
                        one_liner_count = 0
                
                entries.append({
                    'company_id': entry.company_id,
                    'processing_method': entry.processing_method,
                    'limit_value': entry.limit_value,
                    'created_at': entry.created_at,
                    'updated_at': entry.updated_at,
                    'details_length': len(entry.formatted_details) if entry.formatted_details else 0,
                    'one_liner_count': one_liner_count,
                    'has_one_liner': bool(entry.one_liner_json and entry.one_liner_json != "{}")
                })
            
            return entries
            
        except Exception as e:
            logger.error(f"列出OneLiner0529缓存条目时出错: {e}")
            return []
        finally:
            if session:
                await session.close()
    
    def _format_company_details_for_oneliner(self, company_details: Dict[str, Any], limit: int = 20) -> str:
        """
        将公司详情格式化为Markdown文本
        
        Args:
            company_details: 公司详情字典
            limit: 每个API数据的最大条目数，默认20
            
        Returns:
            格式化后的Markdown文本
        """
        formatted_sections = []
        
        # API名称到处理函数的映射
        api_processors = {
            'Patent': process_patent_api_response,
            
            'Products': process_product_api_response,
            
            'News': process_news_api_response,
            
            'Actual Controller': process_controller_api_response,
            
            'Investment': process_investment_api_response,
            
            'Development': process_development_api_response,
            
            'Basic': process_company_api_response,
        }
        
        for api_name, api_data in company_details.items():
            api_data = api_data['result']
            # 如果api_data包含Code=300000，表示无结果，则跳过
            if api_data.find('Code=300000') != -1:
                continue
            
            if not api_data:
                continue
            
            try:
                # 查找匹配的处理函数
                processor_func = None
                api_data = json.loads(api_data)
                
                # 精确匹配
                processor_func = api_processors[api_name]
                
                if processor_func:
                    # 使用对应的处理函数转换为Markdown
                    markdown_content = processor_func(api_data, limit=limit)
                    
                    if markdown_content and markdown_content.strip():
                        formatted_sections.append(markdown_content)
                
            except Exception as e:
                logger.error(f"处理API {api_name} 时出错: {e}")
                # 出错时使用简单格式
                try:
                    content = str(api_data)
                    if content and content.strip():
                        section = f"# {api_name.replace('_', ' ').title()}\n\n{content}\n"
                        formatted_sections.append(section)
                except Exception as e2:
                    logger.error(f"API {api_name} 的备用格式化也失败: {e2}")
                    continue
        
        # 合并所有部分
        final_content = "\n\n---\n\n".join(formatted_sections)
        
        return final_content.strip()
    
    async def process_company_direct(
        self, 
        company_id: str, 
        save_db: bool = True, 
        use_cache: bool = True,
        limit: int = 20,
        # 新增细粒度缓存控制参数
        result_cache: Optional[bool] = None,  # 是否使用结果级缓存（one_liner缓存表）
        api_cache: Optional[bool] = None,     # 是否使用API级缓存（OpenAPI缓存）
        crawl_cache: Optional[bool] = None,   # 是否使用爬取级缓存（网页爬取缓存）
        llm_cache: Optional[bool] = None,     # 是否使用LLM级缓存（LLM响应缓存）
        save_cache: Optional[bool] = None     # 是否保存到缓存（结果保存）
    ) -> Dict[str, Any]:
        """
        直接处理公司，跳过timeline生成，支持分层级缓存控制
        
        Args:
            company_id: 公司ID
            save_db: 是否保存到数据库
            use_cache: 全局缓存开关，如果为False，则所有缓存都不使用
            limit: 每个API数据的最大条目数，默认20
            
            # 细粒度缓存控制参数（None表示使用use_cache的值）
            result_cache: 是否使用结果级缓存（从one_liner缓存表获取完整结果）
            api_cache: 是否使用API级缓存（OpenAPI调用缓存）
            crawl_cache: 是否使用爬取级缓存（网页爬取缓存）
            llm_cache: 是否使用LLM级缓存（LLM响应缓存）
            save_cache: 是否保存结果到缓存表
            
        Returns:
            包含company_details和formatted_details的字典
        """
        # 处理缓存参数的默认值
        if result_cache is None:
            result_cache = use_cache
        if api_cache is None:
            api_cache = use_cache
        if crawl_cache is None:
            crawl_cache = use_cache
        if llm_cache is None:
            llm_cache = use_cache
        if save_cache is None:
            save_cache = save_db  # 默认跟随save_db参数
        
        # 记录缓存配置（用于调试和日志）
        cache_config = {
            "result_cache": result_cache,
            "api_cache": api_cache, 
            "crawl_cache": crawl_cache,
            "llm_cache": llm_cache,
            "save_cache": save_cache
        }
        logger.info(f"处理公司 {company_id}，缓存配置: {cache_config}")
        
        try:
            # 1. 检查结果级缓存
            if result_cache:
                cached_result = await self._get_from_cache(company_id)
                if cached_result:
                    logger.info(f"从结果缓存获取公司 {company_id} 的完整结果")
                    cached_result["method"] = "direct"
                    cached_result["limit"] = limit
                    cached_result["cache_config"] = cache_config
                    return cached_result
            
            # 2. 获取公司详情（使用API缓存）
            logger.info(f"获取公司 {company_id} 详情，API缓存: {api_cache}")
            company_details = await self.company_timeline.get_company_details(
                company_id, use_cache=api_cache
            )
            if not company_details:
                raise ValueError(f"无法获取公司 {company_id} 的详情")
            # 检查公司类型，跳过机关
            orgtype = await self.company_timeline.get_company_orgtype(company_details)
            if orgtype == '机关':
                raise ValueError(f"公司 {company_id} ，跳过处理")

            formatted_details = self._format_company_details_for_oneliner(company_details, limit=limit)
            
            if not formatted_details:
                # 提供更详细的错误信息
                api_status = {}
                for api_name, info in company_details.items():
                    api_status[api_name] = f"类型: {type(info)}, 长度: {len(str(info)) if info else 0}"
                
                error_msg = f"公司 {company_id} 的详情格式化后为空。API状态: {api_status}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # 3. 获取公司主页内容（使用爬取缓存）
            logger.info(f"获取公司 {company_id} 主页内容，爬取缓存: {crawl_cache}")
            homepage_url = await self.company_timeline.get_company_homepage_url(company_details)
            print(homepage_url)
            if homepage_url:
                homepage_markdown = await crawl_company_info(
                    homepage_url, 
                    use_cache=crawl_cache
                )
                print("homepage_markdown:", homepage_markdown)
                formatted_details = f"{formatted_details}\n\n---\n\n{homepage_markdown}"
            
            
            # 4. 生成一句话总结（使用LLM缓存）
            logger.info(f"生成公司 {company_id} 一句话总结，LLM缓存: {llm_cache}")
            one_liners_json = await self.one_liner_generator.generate_one_liners(
                formatted_details, use_cache=llm_cache
            )
            try:
                one_liners = json.loads(one_liners_json)
                if not one_liners:
                    one_liners = {}
            except json.JSONDecodeError as e:
                logger.error(f"解析一句话总结JSON失败: {e}")
                one_liners = {}
            
            # 6. 保存到缓存（如果启用保存缓存）
            if save_cache:
                logger.info(f"保存公司 {company_id} 结果到OneLiner0529缓存")
                await self._save_to_cache(company_id, formatted_details, one_liners, "direct", limit)
                
                # 同时保存到旧的数据库表（为了兼容性）
                if save_db:
                    await self._save_to_db_direct(company_id, company_details, one_liners)
            elif save_db:
                # 如果不保存缓存但需要保存到数据库
                logger.info(f"保存公司 {company_id} 结果到数据库（不保存缓存）")
                await self._save_to_db_direct(company_id, company_details, one_liners)
            
            result = {
                "company_id": company_id,
                "company_details": company_details,
                "formatted_details": formatted_details,
                "one_liners": one_liners,
                "method": "direct",  # 标识使用的是直接方法
                "limit": limit,  # 记录使用的限制数量
                "from_cache": False,
                "cache_config": cache_config  # 记录使用的缓存配置
            }
            
            return result
            
        except Exception as e:
            logger.error(f"处理公司 {company_id} 时出错: {e}")
            raise
    
    async def _save_to_db_direct(
        self, 
        company_id: str, 
        company_details: Dict[str, Any], 
        one_liners: Dict[str, Any]
    ):
        """
        保存直接处理结果到数据库
        
        Args:
            company_id: 公司ID
            company_details: 公司详情
            one_liners: 一句话总结字典
        """
        session = None
        try:
            # 创建异步会话
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 将company_details转换为字符串（用于存储）
            details_text = self._format_company_details_for_oneliner(company_details)
            
            # 检查公司是否已存在
            result = await session.execute(
                select(Company).filter_by(id=company_id)
            )
            existing_company = result.scalars().first()
            
            if existing_company:
                # 更新现有记录
                existing_company.timeline = details_text  # 使用格式化的详情代替timeline
                existing_company.updated_at = datetime.now(timezone.utc)
                
                # 删除旧的一句话总结
                await session.execute(
                    delete(OneLinerModel).where(OneLinerModel.company_id == company_id)
                )
            else:
                # 创建新公司记录
                company = Company(id=company_id, timeline=details_text)
                session.add(company)
            
            # 添加一句话总结
            # 从字典中提取不同格式的总结
            if isinstance(one_liners, dict):
                format_1 = one_liners.get('format_1') or one_liners.get('简短版') or one_liners.get('brief')
                format_2 = one_liners.get('format_2') or one_liners.get('详细版') or one_liners.get('detailed') 
                format_3 = one_liners.get('format_3') or one_liners.get('正式版') or one_liners.get('formal')
            else:
                # 兼容旧的列表格式
                format_1 = one_liners[0] if len(one_liners) > 0 else None
                format_2 = one_liners[1] if len(one_liners) > 1 else None
                format_3 = one_liners[2] if len(one_liners) > 2 else None
            
            one_liner = OneLinerModel(
                company_id=company_id,
                timeline=details_text,  # 保存格式化的详情
                format_1=format_1,
                format_2=format_2,
                format_3=format_3
            )
            session.add(one_liner)
            
            await session.commit()
            
        except SQLAlchemyError as e:
            if session:
                await session.rollback()
            logger.error(f"保存公司 {company_id} 到数据库时出错: {e}")
            raise
        finally:
            if session:
                await session.close()
    
    async def get_company_info(self, company_id: str) -> Optional[Dict[str, Any]]:
        """
        从数据库获取公司信息
        
        Args:
            company_id: 公司ID
            
        Returns:
            公司信息字典，如果不存在则返回None
        """
        session = None
        try:
            # 创建异步会话
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 查询公司信息
            result = await session.execute(
                select(Company).filter_by(id=company_id)
            )
            company = result.scalars().first()
            
            if not company:
                return None
            
            return {
                "id": company.id,
                "timeline": company.timeline,
                "created_at": company.created_at,
                "updated_at": company.updated_at,
                "processing_method": "direct"  # 标识为直接处理方法
            }
            
        except Exception as e:
            logger.error(f"获取公司 {company_id} 信息时出错: {e}")
            return None
        finally:
            if session:
                await session.close()
    
    async def compare_methods(
        self, 
        company_id: str, 
        use_cache: bool = True,
        limit: int = 20
    ) -> Dict[str, Any]:
        """
        比较直接方法和timeline方法的结果
        
        Args:
            company_id: 公司ID
            use_cache: 是否使用缓存
            limit: 每个API数据的最大条目数，默认20
            
        Returns:
            包含两种方法结果的比较字典
        """
        # 导入原始的EntityProcessor
        from .entity_processor import EntityProcessor
        original_processor = EntityProcessor()
        
        try:
            # 1. 使用直接方法
            direct_result = await self.process_company_direct(
                company_id, save_db=False, use_cache=use_cache, limit=limit
            )
            
            # 2. 使用原始方法（timeline方法）
            timeline_result = await original_processor.process_company(
                company_id, save_db=False, use_cache=use_cache
            )
            
            # 3. 比较结果
            comparison = {
                "company_id": company_id,
                "direct_method": {
                    "input_length": len(direct_result.get("formatted_details", "")),
                    "method": "direct",
                    "limit": limit
                },
                "timeline_method": {
                    "timeline_length": len(timeline_result.get("timeline", "")),
                    "method": "timeline"
                },
                "comparison": {
                    "input_length_diff": len(direct_result.get("formatted_details", "")) - len(timeline_result.get("timeline", "")),
                }
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"比较公司 {company_id} 的处理方法时出错: {e}")
            raise
        finally:
            await original_processor.close()


if __name__ == "__main__":
    async def test_direct_processor():
        """测试直接处理器"""
        processor = DirectEntityProcessor()
        
        try:
            # 测试公司ID
            test_company_id = "2349175912"
            
            print(f"测试直接处理公司: {test_company_id}")
            
            # 直接处理，使用默认limit=20
            result = await processor.process_company_direct(test_company_id, save_db=False)
            
            print(f"\n公司详情长度: {len(result['formatted_details'])} 字符")
            print(f"使用的数据限制: {result.get('limit', '未指定')}")
            
            print(f"\n格式化的Markdown详情预览 (前500字符):")
            print(f"{result['formatted_details'][:500]}...")
            
            # 测试不同的limit值
            print(f"\n\n=== 测试不同limit值 ===")
            for test_limit in [5, 10]:
                print(f"\n测试limit={test_limit}:")
                limited_result = await processor.process_company_direct(
                    test_company_id, save_db=False, limit=test_limit
                )
                print(f"  格式化内容长度: {len(limited_result['formatted_details'])} 字符")
            
        except Exception as e:
            print(f"测试失败: {e}")
        finally:
            await processor.close()
    
    asyncio.run(test_direct_processor()) 