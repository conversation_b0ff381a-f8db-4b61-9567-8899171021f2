import os
import asyncio
from datetime import datetime, timezone
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.sql import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from .company_timeline import CompanyTimeline
from .one_liner import OneLiner as OneLinerGenerator
from .models.db_models import init_db, Company, OneLiner


class EntityProcessor:
    """实体处理器，整合公司时间线和一句话总结功能"""
    
    def __init__(self):
        """初始化处理器"""
        # 初始化数据库
        self.engine = init_db()
        self.Session = sessionmaker(bind=self.engine)
        
        # 初始化组件
        self.company_timeline = CompanyTimeline()
        self.one_liner_generator = OneLinerGenerator()
        
        # 创建输出目录
        os.makedirs("./output", exist_ok=True)
    
    async def close(self):
        """关闭数据库连接和清理资源"""
        if hasattr(self.engine, 'dispose'):
            await self.engine.dispose()
        print("数据库连接已关闭")
    
    async def process_company(self, company_id: str, save_db: bool = True, use_cache: bool = True) -> dict:
        """处理单个公司，生成时间线和一句话总结
        
        Args:
            company_id: 公司ID
            save_db: 是否保存到数据库
            use_cache: 是否使用缓存
            
        Returns:
            dict: 包含处理结果的字典
        """
        print(f"开始处理公司: {company_id}")
        
        # 获取公司详情
        company_details = await self.company_timeline.get_company_details(company_id, use_cache=use_cache)
        print(company_details)
        
        # 生成时间线
        print("生成公司时间线...")
        timeline = await self.company_timeline.get_company_timeline(company_details, use_cache=use_cache)
        
        # 生成一句话总结
        print("生成一句话总结...")
        one_liners = await self.one_liner_generator.generate_one_liners(timeline, use_cache=use_cache)
        
        # 保存到数据库
        if save_db:
            print("保存到数据库...")
            await self._save_to_db(company_id, timeline, one_liners)
        
        return {
            "company_id": company_id,
            "timeline": timeline,
            "one_liners": one_liners
        }
    
    async def _save_to_db(self, company_id: str, timeline: str, one_liners: list) -> None:
        """异步保存处理结果到数据库
        
        Args:
            company_id: 公司ID
            timeline: 时间线内容
            one_liners: 一句话总结列表
        """
        session = None
        try:
            # 创建异步会话
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 检查公司是否已存在
            result = await session.execute(
                select(Company).filter_by(id=company_id)
            )
            existing_company = result.scalars().first()
            
            if existing_company:
                # 更新现有记录
                existing_company.timeline = timeline
                existing_company.updated_at = datetime.now(timezone.utc)
                
                # 删除旧的一句话总结
                await session.execute(
                    delete(OneLiner).where(OneLiner.company_id == company_id)
                )
            else:
                # 创建新公司记录
                company = Company(id=company_id, timeline=timeline)
                session.add(company)
            
            # 添加一句话总结
            one_liner = OneLiner(
                company_id=company_id,
                timeline=timeline,  # 保存时间线内容
                format_1=one_liners[0] if len(one_liners) > 0 else None,
                format_2=one_liners[1] if len(one_liners) > 1 else None,
                format_3=one_liners[2] if len(one_liners) > 2 else None
            )
            session.add(one_liner)
            
            await session.commit()
            print(f"数据已保存到数据库，公司ID: {company_id}")
        except SQLAlchemyError as e:
            if session:
                await session.rollback()
            print(f"数据库操作失败: {e}")
        finally:
            if session:
                await session.close()
    
    async def get_company_info(self, company_id: str) -> dict:
        """从数据库异步获取公司信息
        
        Args:
            company_id: 公司ID
            
        Returns:
            dict: 包含公司信息的字典
        """
        session = None
        try:
            # 创建异步会话
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 查询公司信息
            result = await session.execute(
                select(Company).filter_by(id=company_id)
            )
            company = result.scalars().first()
            
            if not company:
                return None
            
            # 查询一句话总结
            result = await session.execute(
                select(OneLiner).filter_by(company_id=company_id)
            )
            one_liner = result.scalars().first()
            
            one_liner_dict = {}
            one_liner_timeline = None
            
            if one_liner:
                if one_liner.timeline:
                    one_liner_timeline = one_liner.timeline
                if one_liner.format_1:
                    one_liner_dict["format_1"] = one_liner.format_1
                if one_liner.format_2:
                    one_liner_dict["format_2"] = one_liner.format_2
                if one_liner.format_3:
                    one_liner_dict["format_3"] = one_liner.format_3
            
            return {
                "id": company.id,
                "timeline": company.timeline,
                "one_liner_timeline": one_liner_timeline,
                "one_liners": one_liner_dict,
                "created_at": company.created_at,
                "updated_at": company.updated_at
            }
        finally:
            if session:
                await session.close()
    
    async def list_companies(self, limit: int = 1000) -> list:
        """异步列出数据库中的公司
        
        Args:
            limit: 返回的最大公司数量
            
        Returns:
            list: 公司信息列表
        """
        session = None
        try:
            # 创建异步会话
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 查询公司列表
            result = await session.execute(
                select(Company).order_by(Company.updated_at.desc()).limit(limit)
            )
            companies = result.scalars().all()
            
            company_list = []
            for company in companies:
                # 查询一句话总结数量
                result = await session.execute(
                    select(OneLiner).filter_by(company_id=company.id)
                )
                one_liners = result.scalars().all()
                
                company_list.append({
                    "id": company.id,
                    "created_at": company.created_at,
                    "updated_at": company.updated_at,
                    "one_liner_count": len(one_liners)
                })
            
            return company_list
        finally:
            if session:
                await session.close()
    
    async def clear_database(self) -> None:
        """清空数据库中的结果表
        
        清空Company和OneLiner表中的所有数据
        """
        session = None
        try:
            # 创建异步会话
            async_session = sessionmaker(
                self.engine, 
                expire_on_commit=False,
                class_=AsyncSession
            )
            
            session = async_session()
            
            # 先删除子表（OneLiner表）中的数据
            await session.execute(delete(OneLiner))
            # 再删除主表（Company表）中的数据
            await session.execute(delete(Company))
            # 提交事务
            await session.commit()
            
            print("数据库中的结果表已清空")
        except SQLAlchemyError as e:
            if session:
                await session.rollback()
            print(f"清空数据库失败: {e}")
        finally:
            if session:
                await session.close() 