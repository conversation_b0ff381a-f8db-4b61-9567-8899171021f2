#!/usr/bin/env python3
"""
实体一句话总结 API 服务
提供基于 company_id 查询一句话总结的 REST API
"""
import os
import sys
import json
from datetime import datetime, timezone
from typing import Optional, List

import uvicorn
from fastapi import FastAPI, HTTPException, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select, func, text

from .models.db_models import Company, OneLiner, OneLiner0529
from .config.mysql_config import get_mysql_url, ENGINE_CONFIG
from .models.api_models import (
    OneLinerResponse, 
    CompanyResponse, 
    Company0529Response,
    OneLinerWithKeywords,
    Keyword,
    CompanyListResponse, 
    CompanyListItem,
    ErrorResponse,
    HealthResponse
)

# 创建FastAPI应用
app = FastAPI(
    title="实体一句话总结 API",
    description="提供基于公司ID查询一句话总结的REST API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# 数据库配置
async_engine = None
async_session = None

async def init_database():
    """初始化数据库连接"""
    global async_engine, async_session
    
    if async_engine is None:
        mysql_async_url = get_mysql_url(async_driver=True)
        async_engine = create_async_engine(mysql_async_url, **ENGINE_CONFIG)
        
        async_session = sessionmaker(
            async_engine,
            expire_on_commit=False,
            class_=AsyncSession
        )

async def get_db_session():
    """获取数据库会话依赖"""
    await init_database()
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close()

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化数据库连接"""
    await init_database()
    print("✅ 数据库连接已初始化")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    global async_engine
    if async_engine:
        await async_engine.dispose()
        print("✅ 数据库连接已关闭")

@app.get("/", summary="根路径", description="API根路径，返回服务信息")
async def root():
    """根路径"""
    return {
        "message": "实体一句话总结 API 服务",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

@app.get("/health", response_model=HealthResponse, summary="健康检查", description="检查API和数据库连接状态")
async def health_check(session: AsyncSession = Depends(get_db_session)):
    """健康检查端点"""
    try:
        # 测试数据库连接
        result = await session.execute(text("SELECT 1"))
        result.fetchone()
        
        return HealthResponse(
            status="healthy",
            database="connected",
            timestamp=datetime.now(timezone.utc)
        )
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"数据库连接失败: {str(e)}"
        )

@app.get(
    "/company/{company_id}/one-liners",
    response_model=OneLinerResponse,
    summary="获取公司一句话总结",
    description="根据公司ID获取对应的三种格式一句话总结"
)
async def get_company_one_liners(
    company_id: str,
    session: AsyncSession = Depends(get_db_session)
):
    """获取特定公司的一句话总结"""
    try:
        # 查询一句话总结
        result = await session.execute(
            select(OneLiner).filter_by(company_id=company_id)
        )
        one_liner = result.scalars().first()
        
        if not one_liner:
            raise HTTPException(
                status_code=404,
                detail=f"未找到公司 {company_id} 的一句话总结"
            )
        
        return OneLinerResponse(
            company_id=one_liner.company_id,
            format_1=one_liner.format_1,
            format_2=one_liner.format_2,
            format_3=one_liner.format_3,
            timeline=one_liner.timeline,
            created_at=one_liner.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询一句话总结时发生错误: {str(e)}"
        )

@app.get(
    "/company/{company_id}",
    response_model=CompanyResponse,
    summary="获取公司完整信息",
    description="根据公司ID获取公司的完整信息，包括时间线和一句话总结"
)
async def get_company_info(
    company_id: str,
    session: AsyncSession = Depends(get_db_session)
):
    """获取特定公司的完整信息"""
    try:
        # 查询公司信息
        result = await session.execute(
            select(Company).filter_by(id=company_id)
        )
        company = result.scalars().first()
        
        if not company:
            raise HTTPException(
                status_code=404,
                detail=f"未找到公司 {company_id}"
            )
        
        # 查询一句话总结
        result = await session.execute(
            select(OneLiner).filter_by(company_id=company_id)
        )
        one_liner = result.scalars().first()
        
        one_liner_data = None
        if one_liner:
            one_liner_data = OneLinerResponse(
                company_id=one_liner.company_id,
                format_1=one_liner.format_1,
                format_2=one_liner.format_2,
                format_3=one_liner.format_3,
                timeline=one_liner.timeline,
                created_at=one_liner.created_at
            )
        
        return CompanyResponse(
            id=company.id,
            timeline=company.timeline,
            one_liners=one_liner_data,
            created_at=company.created_at,
            updated_at=company.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询公司信息时发生错误: {str(e)}"
        )

@app.get(
    "/company_0529/{company_id}",
    response_model=Company0529Response,
    summary="获取公司完整信息（0529版本）",
    description="根据公司ID获取公司的完整信息，包括时间线和原始JSON格式的一句话总结（0529专用端点）"
)
async def get_company_info_0529(
    company_id: str,
    session: AsyncSession = Depends(get_db_session)
):
    """获取特定公司的完整信息（0529版本）"""
    try:
        # 从OneLiner0529表查询
        result = await session.execute(
            select(OneLiner0529).filter_by(company_id=company_id)
        )
        oneliner_0529 = result.scalars().first()
        
        if not oneliner_0529:
            raise HTTPException(
                status_code=404,
                detail=f"未找到公司 {company_id} 的0529版本数据"
            )
        
        # 解析JSON格式并构造Pydantic模型
        one_liners = None
        if oneliner_0529.one_liner_json:
            try:
                data = json.loads(oneliner_0529.one_liner_json)
                if "one_liner" in data and "keywords" in data:
                    # 构造关键词列表
                    keywords = []
                    for kw in data["keywords"]:
                        keywords.append(Keyword(
                            word=kw["word"],
                            start_index=kw["start_index"],
                            end_index=kw["end_index"],
                            source=kw["source"],
                            detail=kw["detail"]
                        ))
                    
                    # 构造包含关键词的一句话总结
                    one_liners = OneLinerWithKeywords(
                        one_liner=data["one_liner"],
                        keywords=keywords
                    )
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回None
                one_liners = None
        
        return Company0529Response(
            id=company_id,
            timeline=oneliner_0529.formatted_details,
            one_liners=one_liners,
            processing_method=oneliner_0529.processing_method,
            limit_value=oneliner_0529.limit_value,
            created_at=oneliner_0529.created_at,
            updated_at=oneliner_0529.updated_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询公司信息时发生错误: {str(e)}"
        )

@app.get(
    "/companies",
    response_model=CompanyListResponse,
    summary="获取公司列表",
    description="获取所有已处理公司的列表，支持分页"
)
async def get_companies_list(
    page: int = Query(1, ge=1, description="页码，从1开始"),
    size: int = Query(20, ge=1, le=100, description="每页数量，最大100"),
    session: AsyncSession = Depends(get_db_session)
):
    """获取公司列表，支持分页"""
    try:
        # 计算偏移量
        offset = (page - 1) * size
        
        # 查询总数
        count_result = await session.execute(
            select(func.count(Company.id))
        )
        total = count_result.scalar()
        
        # 查询公司列表
        result = await session.execute(
            select(Company)
            .order_by(Company.updated_at.desc())
            .offset(offset)
            .limit(size)
        )
        companies = result.scalars().all()
        
        # 构建响应数据
        items = []
        for company in companies:
            # 查询一句话总结数量
            one_liner_result = await session.execute(
                select(func.count(OneLiner.id)).filter_by(company_id=company.id)
            )
            one_liner_count = one_liner_result.scalar()
            
            items.append(CompanyListItem(
                id=company.id,
                created_at=company.created_at,
                updated_at=company.updated_at,
                one_liner_count=one_liner_count
            ))
        
        return CompanyListResponse(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=(total + size - 1) // size  # 向上取整
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"查询公司列表时发生错误: {str(e)}"
        )

@app.get(
    "/search",
    response_model=List[CompanyListItem],
    summary="搜索公司",
    description="根据公司ID模糊搜索公司"
)
async def search_companies(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="返回结果数量限制"),
    session: AsyncSession = Depends(get_db_session)
):
    """搜索公司"""
    try:
        # 模糊搜索公司ID
        result = await session.execute(
            select(Company)
            .filter(Company.id.like(f"%{q}%"))
            .order_by(Company.updated_at.desc())
            .limit(limit)
        )
        companies = result.scalars().all()
        
        # 构建响应数据
        items = []
        for company in companies:
            # 查询一句话总结数量
            one_liner_result = await session.execute(
                select(func.count(OneLiner.id)).filter_by(company_id=company.id)
            )
            one_liner_count = one_liner_result.scalar()
            
            items.append(CompanyListItem(
                id=company.id,
                created_at=company.created_at,
                updated_at=company.updated_at,
                one_liner_count=one_liner_count
            ))
        
        return items
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"搜索公司时发生错误: {str(e)}"
        )

if __name__ == "__main__":
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 