#!/usr/bin/env python3
"""
实体一句话总结 API 客户端示例
展示如何使用 API 服务获取公司信息
"""
import asyncio
import aiohttp
import json
from typing import Optional, List, Dict, Any


class OneLinerAPIClient:
    """一句话总结 API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _request(self, method: str, path: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{path}"
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                if response.content_type == 'application/json':
                    data = await response.json()
                else:
                    data = await response.text()
                
                if 200 <= response.status < 300:
                    return {"success": True, "data": data}
                else:
                    return {"success": False, "error": data, "status": response.status}
                    
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return await self._request("GET", "/health")
    
    async def get_one_liners(self, company_id: str) -> Dict[str, Any]:
        """获取公司一句话总结"""
        return await self._request("GET", f"/company/{company_id}/one-liners")
    
    async def get_company_info(self, company_id: str) -> Dict[str, Any]:
        """获取公司完整信息"""
        return await self._request("GET", f"/company/{company_id}")
    
    async def get_companies_list(self, page: int = 1, size: int = 20) -> Dict[str, Any]:
        """获取公司列表"""
        return await self._request("GET", f"/companies?page={page}&size={size}")
    
    async def search_companies(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """搜索公司"""
        return await self._request("GET", f"/search?q={query}&limit={limit}")


async def demo_api_usage():
    """演示API使用"""
    print("🚀 实体一句话总结 API 客户端演示\n")
    
    async with OneLinerAPIClient() as client:
        # 1. 健康检查
        print("1️⃣ 健康检查...")
        result = await client.health_check()
        if result["success"]:
            print(f"✅ API 服务正常运行")
            print(f"   数据库状态: {result['data']['database']}")
        else:
            print(f"❌ API 服务异常: {result['error']}")
            return
        print()
        
        # 2. 获取公司列表
        print("2️⃣ 获取公司列表...")
        result = await client.get_companies_list(page=1, size=5)
        if result["success"]:
            companies = result["data"]["companies"]
            total = result["data"]["total"]
            print(f"✅ 共有 {total} 家公司，显示前 {len(companies)} 家:")
            for company in companies:
                status = "✅有" if company["has_one_liner"] else "❌无"
                print(f"   - {company['id']} ({status}一句话总结)")
            
            # 选择第一家有一句话总结的公司进行演示
            demo_company_id = None
            for company in companies:
                if company["has_one_liner"]:
                    demo_company_id = company["id"]
                    break
                    
        else:
            print(f"❌ 获取公司列表失败: {result['error']}")
            return
        print()
        
        if not demo_company_id:
            print("⚠️  没有找到有一句话总结的公司，创建一些测试数据后再试")
            return
        
        # 3. 获取特定公司的一句话总结
        print(f"3️⃣ 获取公司 {demo_company_id} 的一句话总结...")
        result = await client.get_one_liners(demo_company_id)
        if result["success"]:
            data = result["data"]
            print(f"✅ 一句话总结:")
            print(f"   格式1: {data.get('format_1', 'N/A')}")
            print(f"   格式2: {data.get('format_2', 'N/A')}")
            print(f"   格式3: {data.get('format_3', 'N/A')}")
            print(f"   创建时间: {data.get('created_at', 'N/A')}")
        else:
            print(f"❌ 获取一句话总结失败: {result['error']}")
        print()
        
        # 4. 获取公司完整信息
        print(f"4️⃣ 获取公司 {demo_company_id} 的完整信息...")
        result = await client.get_company_info(demo_company_id)
        if result["success"]:
            data = result["data"]
            print(f"✅ 公司完整信息:")
            print(f"   公司ID: {data['id']}")
            print(f"   时间线长度: {len(data.get('timeline', '')) if data.get('timeline') else 0} 字符")
            print(f"   最后更新: {data.get('updated_at', 'N/A')}")
        else:
            print(f"❌ 获取公司信息失败: {result['error']}")
        print()
        
        # 5. 搜索功能演示
        search_query = demo_company_id[:6]  # 使用ID的前几位搜索
        print(f"5️⃣ 搜索包含 '{search_query}' 的公司...")
        result = await client.search_companies(search_query, limit=3)
        if result["success"]:
            companies = result["data"]
            print(f"✅ 找到 {len(companies)} 家匹配的公司:")
            for company in companies:
                status = "✅" if company["has_one_liner"] else "❌"
                print(f"   - {company['id']} {status}")
        else:
            print(f"❌ 搜索失败: {result['error']}")
        print()
        
        # 6. 测试不存在的公司
        print("6️⃣ 测试查询不存在的公司...")
        result = await client.get_one_liners("*********")
        if result["success"]:
            print("❓ 意外的成功响应")
        else:
            print(f"✅ 正确处理了不存在的公司: {result.get('status', 'N/A')}")
        print()
        
        print("🎉 API 演示完成!")


async def interactive_mode():
    """交互模式"""
    print("🎮 进入交互模式，输入公司ID查询一句话总结")
    print("输入 'quit' 或 'exit' 退出\n")
    
    async with OneLinerAPIClient() as client:
        while True:
            try:
                company_id = input("请输入公司ID: ").strip()
                
                if company_id.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                
                if not company_id:
                    continue
                
                print(f"\n🔍 查询公司 {company_id}...")
                result = await client.get_one_liners(company_id)
                
                if result["success"]:
                    data = result["data"]
                    print("✅ 一句话总结:")
                    print(f"   格式1: {data.get('format_1', 'N/A')}")
                    print(f"   格式2: {data.get('format_2', 'N/A')}")
                    print(f"   格式3: {data.get('format_3', 'N/A')}")
                else:
                    print(f"❌ 查询失败: {result.get('error', 'Unknown error')}")
                print()
                
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")


async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        await interactive_mode()
    else:
        await demo_api_usage()


if __name__ == "__main__":
    asyncio.run(main()) 