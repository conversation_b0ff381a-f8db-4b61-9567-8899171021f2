import asyncio
import json
from src.clients import TycOpenApiToolClient, APICall
from src.modules import TimelineGenerator
from .config.api_config import COMPANY_CALL_API_ID_LIST
from .config.cache_config import get_cache_instance


class CompanyTimeline:
    """
    公司时间线
    """
    def __init__(self):
        self.timeline_generator = TimelineGenerator()
        # 延迟初始化缓存实例
        self._cache = None
    
    @property
    def cache(self):
        """延迟初始化缓存实例"""
        if self._cache is None:
            self._cache = get_cache_instance('timeline')
        return self._cache
    
    async def get_company_details(self, company_id: str, use_cache: bool = True) -> dict:
        """
        获取公司详情
        """
        # 检查缓存
        if use_cache:
            cache_key = f"company_details:{company_id}"
            cached_result = await self.cache.get(cache_key)
            if cached_result is not None:
                print(f"从缓存中获取公司详情: {company_id}")
                return cached_result

        # 缓存未命中，从API获取数据
        async with TycOpenApiToolClient() as client:
            api_calls = [
                APICall(api_id=api_id, name=api_name, request_params={"keyword": company_id}, full_response=True, response_format="json")
                for api_name, api_id in COMPANY_CALL_API_ID_LIST
            ]
            company_info = await client.execute_apis(api_calls, use_cache=use_cache)
            # 缓存结果
            if use_cache:
                await self.cache.set(cache_key, company_info)
        
        return company_info

    async def get_company_orgtype(self, company_info: dict) -> str:
        """
        获取公司类型
        """
        try:
            orgtype = json.loads(company_info['Basic']['result'])['result']['companyOrgType']
        except Exception as e:
            print(f"获取公司类型失败: {e}")
            return ""
        return orgtype


    async def get_company_homepage_url(self, company_info: dict) -> str:
        """
        获取公司主页URL
        """
        try:
            homepage_url = json.loads(company_info['Basic']['result'])['result']['websiteList']
            if isinstance(homepage_url, list) and len(homepage_url) > 0:
                return homepage_url[0]
            elif isinstance(homepage_url, str):
                return homepage_url
            else:
                return ""
        except Exception as e:
            print(f"获取公司主页URL失败: {e}")
            return ""

    async def get_company_timeline(self, company_details: dict, use_cache: bool = True) -> str:
        """
        获取公司时间线
        """
        # 检查缓存
        if use_cache:
            # 使用公司详情的哈希作为缓存键
            company_details_str = json.dumps(company_details, sort_keys=True)
            import hashlib
            cache_key = f"company_timeline:{hashlib.md5(company_details_str.encode()).hexdigest()}"
            
            cached_result = await self.cache.get(cache_key)
            if cached_result is not None:
                print("从缓存中获取公司时间线")
                return cached_result

        # 缓存未命中，生成时间线
        timeline = await self.timeline_generator.generate_timeline(company_details, use_cache=use_cache)
        print(timeline)
        
        # 缓存结果
        if use_cache:
            await self.cache.set(cache_key, timeline)
            
        return timeline
    
    async def close(self):
        """关闭相关资源"""
        # 关闭缓存资源
        if hasattr(self._cache, 'close') and self._cache is not None:
            await self._cache.close()
        
        # 关闭时间线生成器的LLM客户端
        if hasattr(self.timeline_generator, 'llm_client') and hasattr(self.timeline_generator.llm_client, 'close'):
            await self.timeline_generator.llm_client.close()


if __name__ == "__main__":
    company_timeline = CompanyTimeline()
    company_id = "2349175912"
    company_details = asyncio.run(company_timeline.get_company_details(company_id))
    with open("company_details.txt", "w") as f:
        for api_name, info in company_details.items():
            f.write(f"{api_name}\n")
            f.write(f"{info}\n")
            f.write("-" * 100+"\n")
    print("company_details.txt 已保存")
    timeline = asyncio.run(company_timeline.get_company_timeline(company_details))
    with open("company_timeline.txt", "w") as f:
        f.write(timeline)
    print("company_timeline.txt 已保存")