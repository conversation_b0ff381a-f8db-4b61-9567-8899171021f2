#!/usr/bin/env python3
"""
测试爬取缓存单例模式
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.clients.crawl_markdown_client import crawl_company_info


async def test_crawl_singleton():
    """测试爬取缓存单例模式"""
    print('🧪 测试爬取缓存单例模式...')
    
    # 多次调用同一个函数，应该只初始化一次
    for i in range(3):
        print(f'第{i+1}次调用...')
        result = await crawl_company_info('http://www.delong-grp.com')
        print(f'结果类型: {type(result)}')
        if result:
            print(f'结果长度: {len(str(result))} 字符')
    
    print('✅ 测试完成')


if __name__ == "__main__":
    asyncio.run(test_crawl_singleton()) 