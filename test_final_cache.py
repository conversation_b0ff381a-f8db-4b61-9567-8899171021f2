#!/usr/bin/env python3
"""
最终测试：验证所有缓存单例模式
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from src.direct_entity_processor import DirectEntityProcessor


async def final_cache_test():
    """最终测试所有缓存的单例模式"""
    print('🧪 最终测试：验证所有缓存单例模式...')
    
    processor = DirectEntityProcessor()
    
    try:
        result = await processor.process_company_direct(
            company_id='78941085',
            save_db=False,
            use_cache=True,
            result_cache=False  # 不使用结果缓存，确保执行完整流程
        )
        print('✅ 完整流程测试成功')
        
        if result.get('one_liners'):
            print(f"📝 One-liner结果类型: {type(result['one_liners'])}")
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
    finally:
        await processor.close()
        print('🔚 资源已清理')


if __name__ == "__main__":
    asyncio.run(final_cache_test()) 